using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace French_Press_POS
{
    public partial class ManualPaymentForm : Form
    {
        private VivaWalletApiClient _vivaClient;
        private decimal _amount;
        private string _currency;

        public ManualPaymentForm(decimal amount = 1.00m, string currency = "EUR")
        {
            InitializeComponent();
            _amount = amount;
            _currency = currency;
            _vivaClient = new VivaWalletApiClient();
            
            // Set default values
            txtAmount.Text = _amount.ToString("F2");
            txtAuthCode.Text = VivaWalletConfig.TestAuthCode;
            
            // Configure form
            this.Text = "Manual Payment Entry - Viva Wallet";
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterParent;
        }

        private async void btnProcessPayment_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                btnProcessPayment.Enabled = false;
                btnProcessPayment.Text = "Processing...";
                lblStatus.Text = "Processing payment...";
                lblStatus.ForeColor = Color.Blue;

                // Validate Viva Wallet configuration
                if (!VivaWalletConfig.ValidateConfig())
                {
                    MessageBox.Show("Viva Wallet configuration is incomplete. Please check your settings.", 
                        "Configuration Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Create manual payment request
                var paymentRequest = new ManualPaymentRequest
                {
                    Amount = decimal.Parse(txtAmount.Text),
                    CardNumber = txtCardNumber.Text.Replace(" ", "").Replace("-", ""),
                    ExpiryDate = txtExpiryDate.Text,
                    CVV = txtCVV.Text,
                    AuthorizationCode = txtAuthCode.Text
                };

                // Process payment through Viva Wallet
                var response = await _vivaClient.ProcessManualPaymentAsync(paymentRequest);

                if (response != null && !string.IsNullOrEmpty(response.transactionId))
                {
                    lblStatus.Text = "Payment successful!";
                    lblStatus.ForeColor = Color.Green;

                    // Generate and print receipt
                    var receiptGenerator = new ReceiptGenerator();
                    var receiptData = new ReceiptData
                    {
                        TransactionId = response.transactionId,
                        Amount = response.amount,
                        Currency = response.currency,
                        AuthorizationCode = response.authorizationCode,
                        MaskedCardNumber = response.maskedCardNumber,
                        Timestamp = response.timestamp,
                        MerchantId = VivaWalletConfig.MerchantId,
                        TerminalId = VivaWalletConfig.TerminalId,
                        Function101_3Enabled = VivaWalletConfig.EnableFunction101_3
                    };

                    receiptGenerator.PrintReceipt(receiptData);

                    MessageBox.Show($"Payment processed successfully!\n\nTransaction ID: {response.transactionId}\nAuth Code: {response.authorizationCode}\n\nReceipt has been printed.", 
                        "Payment Success", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    lblStatus.Text = "Payment failed - no response received";
                    lblStatus.ForeColor = Color.Red;
                    MessageBox.Show("Payment processing failed. Please try again.", 
                        "Payment Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (VivaWalletException ex)
            {
                lblStatus.Text = "Payment failed";
                lblStatus.ForeColor = Color.Red;
                MessageBox.Show($"Viva Wallet Error: {ex.Message}", 
                    "Payment Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Payment failed";
                lblStatus.ForeColor = Color.Red;
                MessageBox.Show($"Error processing payment: {ex.Message}", 
                    "Payment Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnProcessPayment.Enabled = true;
                btnProcessPayment.Text = "Process Payment";
            }
        }

        private bool ValidateInput()
        {
            // Validate amount
            if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("Please enter a valid amount.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAmount.Focus();
                return false;
            }

            // Validate card number (basic check)
            var cardNumber = txtCardNumber.Text.Replace(" ", "").Replace("-", "");
            if (string.IsNullOrEmpty(cardNumber) || cardNumber.Length < 13 || cardNumber.Length > 19)
            {
                MessageBox.Show("Please enter a valid card number.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCardNumber.Focus();
                return false;
            }

            // Validate expiry date (MM/YY format)
            if (string.IsNullOrEmpty(txtExpiryDate.Text) || txtExpiryDate.Text.Length != 5 || !txtExpiryDate.Text.Contains("/"))
            {
                MessageBox.Show("Please enter expiry date in MM/YY format.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtExpiryDate.Focus();
                return false;
            }

            // Validate CVV
            if (string.IsNullOrEmpty(txtCVV.Text) || txtCVV.Text.Length < 3 || txtCVV.Text.Length > 4)
            {
                MessageBox.Show("Please enter a valid CVV (3-4 digits).", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCVV.Focus();
                return false;
            }

            // Validate authorization code
            if (string.IsNullOrEmpty(txtAuthCode.Text))
            {
                MessageBox.Show("Please enter an authorization code.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAuthCode.Focus();
                return false;
            }

            return true;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtCardNumber_TextChanged(object sender, EventArgs e)
        {
            // Format card number with spaces
            var textBox = sender as TextBox;
            var text = textBox.Text.Replace(" ", "");
            var formattedText = "";
            
            for (int i = 0; i < text.Length; i++)
            {
                if (i > 0 && i % 4 == 0)
                    formattedText += " ";
                formattedText += text[i];
            }
            
            if (formattedText != textBox.Text)
            {
                textBox.Text = formattedText;
                textBox.SelectionStart = formattedText.Length;
            }
        }

        private void txtExpiryDate_TextChanged(object sender, EventArgs e)
        {
            // Format expiry date as MM/YY
            var textBox = sender as TextBox;
            var text = textBox.Text.Replace("/", "");
            
            if (text.Length >= 2)
            {
                var formattedText = text.Substring(0, 2);
                if (text.Length > 2)
                    formattedText += "/" + text.Substring(2, Math.Min(2, text.Length - 2));
                
                if (formattedText != textBox.Text)
                {
                    textBox.Text = formattedText;
                    textBox.SelectionStart = formattedText.Length;
                }
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _vivaClient?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
