{}[2024-04-22 10:37:29.319]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:37:29.351]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:37:29.382]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:37:29.397]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:37:30.257]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]10.60[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]z
{}[2024-04-22 10:37:39.698]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054686[1f]054686[1f]03RLWV9WU9XENVJNVN3[1f]1[1f]032022[1f][1f][1f][1c]03[1c]1060[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240422103730[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221037300524[1f]TC=302B46906CB9773F[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00DF[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]s
{}[2024-04-22 10:37:39.729]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-22 10:37:39.745]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:37:39.792]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:37:39.807]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:37:39.823]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:37:39.838]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:37:39.870]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]10.60[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]S
{}[2024-04-22 10:37:44.066]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLWV9D2VXDPF4NVN4[1f]2[1f]032022[1f][1f][1f][1c]04[1c]1060[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240422103740[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221037401129[1f]TC=302B46906CB9773F[1f]TVR=0000000000[1f]ATC=00DF[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]8
{}[2024-04-22 10:37:44.097]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-04-22 10:37:44.113]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:37:53.976]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:37:54.008]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:37:54.023]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:37:54.055]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:37:54.211]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]02[1c]10.60[1c][1c]2[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]x
{}[2024-04-22 10:38:01.735]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054691[1f]054691[1f]03RLWV9DK06TUK67VN6[1f]3[1f]032022[1f][1f][1f][1c]02[1c]1060[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]3[1f]2[1f]20240422103754[1f][1f][1f][1f]3[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221037541205[1f]TC=3A513F8ED51E2021[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00E0[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][05]
{}[2024-04-22 10:38:01.766]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_RETURN Success!
{}[2024-04-22 10:38:01.798]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:42:07.829]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:42:07.845]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:42:07.861]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:42:07.876]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:42:08.111]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]16[1c][1c][1c]3[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]{
{}[2024-04-22 10:42:08.595]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100021[1c]ALREADY COMPL.[03]o
{}[2024-04-22 10:42:08.626]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_VOID Fail!,ResultTxt =ALREADY COMPL.
{}[2024-04-22 10:42:08.642]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:42:25.914]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:42:25.930]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:42:25.945]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:42:25.961]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:42:26.086]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]16[1c][1c][1c]3[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]U
{}[2024-04-22 10:42:44.162]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLWV9M86NXQAUZVNB[1f][1f]032022[1f][1f][1f][1c]18[1c]1060[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f]43415244484F4C4445522F56495341[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]4[1f]3[1f]20240422104226[1f][1f][1f][1f][1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221042260849[1f]TC=3A513F8ED51E2021[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]]
{}[2024-04-22 10:42:44.193]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_VOID Success!
{}[2024-04-22 10:42:44.209]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:42:58.256]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:42:58.271]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:42:58.287]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:42:58.303]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:42:58.334]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-04-22 10:43:00.600]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLWV9MPXUBHAEGVND[1f]0[1f]032022[1f][1f][1f][1c]1=0=0=0=0=0[1c]1060=0=0=0=0=0[1c]20240422104300[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]3
{}[2024-04-22 10:43:00.631]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-04-22 10:43:00.662]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:45:32.492]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:45:32.523]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:45:32.554]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:45:32.570]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:45:32.867]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]1.06[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]J
{}[2024-04-22 10:45:42.236]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054707[1f]054707[1f]03RLWV9RLB0KEWN2VNH[1f]1[1f]042201[1f][1f][1f][1c]03[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240422104533[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221045337056[1f]TC=F64D3EB900CF5C7D[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00E1[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]0
{}[2024-04-22 10:45:42.268]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-22 10:45:42.299]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:45:42.346]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:45:42.361]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:45:42.377]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:45:42.393]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:45:42.424]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]1.06[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]c
{}[2024-04-22 10:45:46.293]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLWV9RPUFXJDDRVNX[1f]2[1f]042201[1f][1f][1f][1c]04[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240422104542[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221045429210[1f]TC=F64D3EB900CF5C7D[1f]TVR=0000000000[1f]ATC=00E1[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][18]
{}[2024-04-22 10:45:46.324]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-04-22 10:45:46.355]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:46:27.939]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:46:27.970]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:46:28.002]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:46:28.033]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:46:28.158]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]3.18[1c][1c]3[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]E
{}[2024-04-22 10:46:36.671]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054709[1f]054709[1f]03RLWV9TB2GG95TKVNK[1f]3[1f]042201[1f][1f][1f][1c]03[1c]318[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]3[1f]3[1f]20240422104628[1f][1f][1f][1f]3[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221046287243[1f]TC=BFC6CC6CDC1E428B[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00E2[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]C
{}[2024-04-22 10:46:36.703]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-22 10:46:36.734]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:46:45.226]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:46:45.242]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:46:45.258]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:46:45.273]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:46:45.367]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]16[1c][1c][1c]3[1f][1f][1f]3[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]y
{}[2024-04-22 10:46:49.080]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054710[1f]054710[1f]03RLWV9TN7R4644WVNL[1f][1f]042201[1f][1f][1f][1c]19[1c]318[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f]43415244484F4C4445522F56495341[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]4[1f]3[1f]20240422104645[1f][1f][1f][1f][1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221046454876[1f]TC=BFC6CC6CDC1E428B[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]R
{}[2024-04-22 10:46:49.111]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_VOID Success!
{}[2024-04-22 10:46:49.142]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:54:27.982]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:54:28.013]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:54:28.045]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:54:28.076]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:54:28.326]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-04-22 10:54:30.248]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLWVABRW6LF6APVY7[1f]0[1f]042201[1f][1f][1f][1c]1=0=0=0=0=0[1c]106=0=0=0=0=0[1c]20240422105430[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03][1b]
{}[2024-04-22 10:54:30.279]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-04-22 10:54:30.310]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:54:40.390]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:54:40.421]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:54:40.468]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:54:40.500]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:54:40.609]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]5.30[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]K
{}[2024-04-22 10:54:48.765]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054742[1f]054742[1f]03RLWVAWBJJ0UGGUVY8[1f]1[1f]042202[1f][1f][1f][1c]03[1c]530[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240422105440[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221054407421[1f]TC=274432304AA337FA[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00E3[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]I
{}[2024-04-22 10:54:48.796]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-22 10:54:48.827]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:55:07.063]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:55:07.095]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:55:07.126]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:55:07.157]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:55:07.345]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]16[1c][1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]y
{}[2024-04-22 10:55:11.201]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054744[1f]054744[1f]03RLWVAD1G4KLN084BU[1f][1f]042202[1f][1f][1f][1c]19[1c]530[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f]43415244484F4C4445522F56495341[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240422105507[1f][1f][1f][1f][1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221055079426[1f]TC=274432304AA337FA[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]7
{}[2024-04-22 10:55:11.232]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_VOID Success!
{}[2024-04-22 10:55:11.263]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:55:31.181]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:55:31.213]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:55:31.244]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:55:31.275]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:55:31.603]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]11.66[1c][1c]3[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]
{}[2024-04-22 10:55:39.248]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054745[1f]054745[1f]03RLWVADT0EX9UT4VYA[1f]3[1f]042202[1f][1f][1f][1c]03[1c]1166[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]3[1f]3[1f]20240422105531[1f][1f][1f][1f]3[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221055318034[1f]TC=70A476A4545BCD00[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00E4[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]n
{}[2024-04-22 10:55:39.279]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-22 10:55:39.310]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 10:55:39.357]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 10:55:39.388]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 10:55:39.420]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 10:55:39.451]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 10:55:39.482]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]11.66[1c][1c]3[1f][1f][1f]3[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]T
{}[2024-04-22 10:55:43.435]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLWVAE13K3T6P6VYB[1f]4[1f]042202[1f][1f][1f][1c]04[1c]1166[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]4[1f]3[1f]20240422105539[1f][1f][1f][1f]4[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221055390278[1f]TC=70A476A4545BCD00[1f]TVR=0000000000[1f]ATC=00E4[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]O
{}[2024-04-22 10:55:43.466]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-04-22 10:55:43.497]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 11:02:46.282]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 11:02:46.313]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 11:02:46.344]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 11:02:46.376]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 11:02:46.610]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]02[1c]11.66[1c][1c]4[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]y
{}[2024-04-22 11:02:55.800]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054761[1f]054761[1f]03RLWVAR71K72F9VVYY[1f]5[1f]042202[1f][1f][1f][1c]02[1c]1166[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]5[1f]4[1f]20240422110246[1f][1f][1f][1f]5[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221102463709[1f]TC=F4EDAE8A85108B7B[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00E5[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][0d]
{}[2024-04-22 11:02:55.831]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_RETURN Success!
{}[2024-04-22 11:02:55.862]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 11:03:11.014]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 11:03:11.045]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 11:03:11.077]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 11:03:11.108]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 11:03:11.186]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]15.90[1c][1c]6[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]w
{}[2024-04-22 11:03:19.962]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054762[1f]054762[1f]03RLWVARUQ8BT8HXVYQ[1f]6[1f]042202[1f][1f][1f][1c]03[1c]1590[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]6[1f]6[1f]20240422110311[1f][1f][1f][1f]6[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221103116965[1f]TC=47BE9218D1786712[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00E6[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][1a]
{}[2024-04-22 11:03:19.993]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-22 11:03:20.025]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 11:03:35.743]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 11:03:35.774]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 11:03:35.805]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 11:03:35.836]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 11:03:36.066]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]33[1c]2.12[1c][1c]6[1f][1f][1f]6[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]a
{}[2024-04-22 11:03:39.873]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054763[1f]054763[1f]03RLWVARUQ8BT8HXVYQ[1f]7[1f]042202[1f][1f][1f][1c]33[1c]1802[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c][1f]0[1f][1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]6[1f]6[1f]20240422110336[1f][1f][1f][1f]7[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221103364838[1f]TC=47BE9218D1786712[1f]TVR=0000000000[1f]ATC=00E6[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]o
{}[2024-04-22 11:03:39.904]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_INCREMENTALAUTH Success!
{}[2024-04-22 11:03:39.935]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 11:03:49.190]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 11:03:49.221]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 11:03:49.252]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 11:03:49.283]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 11:03:49.596]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]33[1c]2.00[1c][1c]6[1f][1f][1f]6[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]b
{}[2024-04-22 11:03:53.340]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054764[1f]054764[1f]03RLWVARUQ8BT8HXVYQ[1f]8[1f]042202[1f][1f][1f][1c]33[1c]2002[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c][1f]0[1f][1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]6[1f]6[1f]20240422110349[1f][1f][1f][1f]8[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221103498512[1f]TC=47BE9218D1786712[1f]TVR=0000000000[1f]ATC=00E6[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]m
{}[2024-04-22 11:03:53.371]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_INCREMENTALAUTH Success!
{}[2024-04-22 11:03:53.402]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 11:03:58.442]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 11:03:58.473]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 11:03:58.505]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 11:03:58.536]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 11:03:58.802]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]20.02[1c][1c]6[1f][1f][1f]6[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]T
{}[2024-04-22 11:04:02.536]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLWVAT8KBN8U9UVYT[1f]9[1f]042202[1f][1f][1f][1c]04[1c]2002[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]7[1f]6[1f]20240422110359[1f][1f][1f][1f]9[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221103591156[1f]TC=47BE9218D1786712[1f]TVR=0000000000[1f]ATC=00E6[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]K
{}[2024-04-22 11:04:02.567]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-04-22 11:04:02.598]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 11:06:11.056]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 11:06:11.087]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 11:06:11.118]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 11:06:11.149]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 11:06:11.212]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-04-22 11:06:13.440]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLWVB19VVJVDELVP2[1f]0[1f]042202[1f][1f][1f][1c]3=0=0=0=0=0[1c]2002=0=0=0=0=0[1c]20240422110613[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03] 
{}[2024-04-22 11:06:13.471]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-04-22 11:06:13.502]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 11:06:24.647]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 11:06:24.678]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 11:06:24.725]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 11:06:24.756]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 11:06:25.037]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]4.24[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]O
{}[2024-04-22 11:06:31.978]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054783[1f]054783[1f]03RLWVB1Q9G9R455VP3[1f]1[1f]042203[1f][1f][1f][1c]03[1c]424[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240422110625[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221106252936[1f]TC=7EF180F88F27CF77[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00E7[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]-
{}[2024-04-22 11:06:32.010]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-22 11:06:32.025]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 11:06:40.149]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 11:06:40.180]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 11:06:40.211]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 11:06:40.242]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 11:06:40.399]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]33[1c]2.12[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]a
{}[2024-04-22 11:06:44.295]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054787[1f]054787[1f]03RLWVB1Q9G9R455VP3[1f]2[1f]042203[1f][1f][1f][1c]33[1c]636[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c][1f]0[1f][1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240422110640[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221106400203[1f]TC=7EF180F88F27CF77[1f]TVR=0000000000[1f]ATC=00E7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][
{}[2024-04-22 11:06:44.326]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_INCREMENTALAUTH Success!
{}[2024-04-22 11:06:44.357]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 11:07:01.406]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 11:07:01.422]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 11:07:01.453]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 11:07:01.484]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 11:07:01.598]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]33[1c]2.00[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]b
{}[2024-04-22 11:07:05.509]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054789[1f]054789[1f]03RLWVB1Q9G9R455VP3[1f]3[1f]042203[1f][1f][1f][1c]33[1c]836[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c][1f]0[1f][1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240422110701[1f][1f][1f][1f]3[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221107012628[1f]TC=7EF180F88F27CF77[1f]TVR=0000000000[1f]ATC=00E7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]Z
{}[2024-04-22 11:07:05.549]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_INCREMENTALAUTH Success!
{}[2024-04-22 11:07:05.569]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-22 11:07:09.769]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-22 11:07:09.804]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-22 11:07:09.824]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-22 11:07:09.839]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-22 11:07:09.899]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]33[1c]2.12[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]a
{}[2024-04-22 11:07:13.829]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 054790[1f]054790[1f]03RLWVB1Q9G9R455VP3[1f]4[1f]042203[1f][1f][1f][1c]33[1c]1048[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c][1f]0[1f][1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240422110710[1f][1f][1f][1f]4[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404221107102229[1f]TC=7EF180F88F27CF77[1f]TVR=0000000000[1f]ATC=00E7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]o
{}[2024-04-22 11:07:13.864]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_INCREMENTALAUTH Success!
{}[2024-04-22 11:07:13.884]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
