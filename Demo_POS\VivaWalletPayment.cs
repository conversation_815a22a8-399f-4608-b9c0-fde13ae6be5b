using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSLink;

namespace French_Press_POS
{
    /// <summary>
    /// Enhanced payment processing class that integrates Viva Wallet with PAX terminal
    /// </summary>
    public static class VivaWalletPayment
    {
        private static VivaWalletApiClient _vivaClient;
        private static bool _isInitialized = false;

        /// <summary>
        /// Initialize Viva Wallet payment system
        /// </summary>
        public static async Task<bool> InitializeAsync()
        {
            try
            {
                if (!VivaWalletConfig.ValidateConfig())
                {
                    MessageBox.Show("Viva Wallet configuration is incomplete. Please check your settings.", 
                        "Configuration Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }

                _vivaClient = new VivaWalletApiClient();
                var authResult = await _vivaClient.AuthenticateAsync();
                
                if (authResult)
                {
                    _isInitialized = true;
                    Console.WriteLine("Viva Wallet payment system initialized successfully");
                    return true;
                }
                else
                {
                    MessageBox.Show("Failed to authenticate with Viva Wallet. Please check your credentials.", 
                        "Authentication Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing Viva Wallet: {ex.Message}");
                MessageBox.Show($"Error initializing Viva Wallet: {ex.Message}", 
                    "Initialization Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Process manual payment entry
        /// </summary>
        public static async Task<string[]> ProcessManualPaymentAsync(decimal amount, string currency = "EUR")
        {
            try
            {
                if (!_isInitialized)
                {
                    var initResult = await InitializeAsync();
                    if (!initResult)
                    {
                        return new string[] { "999999", "Initialization failed" };
                    }
                }

                // Show manual payment form
                using (var manualForm = new ManualPaymentForm(amount, currency))
                {
                    var result = manualForm.ShowDialog();
                    
                    if (result == DialogResult.OK)
                    {
                        return new string[] { "000000", "Manual payment processed successfully" };
                    }
                    else
                    {
                        return new string[] { "999998", "Manual payment cancelled" };
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Manual payment error: {ex.Message}");
                return new string[] { "999997", $"Manual payment error: {ex.Message}" };
            }
        }

        /// <summary>
        /// Process contactless payment through PAX terminal with Viva Wallet
        /// </summary>
        public static async Task<string[]> ProcessContactlessPaymentAsync(decimal amount, string currency = "EUR")
        {
            try
            {
                if (!_isInitialized)
                {
                    var initResult = await InitializeAsync();
                    if (!initResult)
                    {
                        return new string[] { "999999", "Initialization failed" };
                    }
                }

                // Create payment order with Viva Wallet first
                var orderRequest = new PaymentOrderRequest
                {
                    amount = (long)(amount * 100), // Convert to cents
                    customerTrns = $"Contactless Payment - {DateTime.Now:yyyyMMddHHmmss}",
                    customer = new Customer
                    {
                        email = "<EMAIL>",
                        fullName = "Contactless Customer"
                    },
                    paymentTimeout = 300,
                    preauth = false,
                    allowRecurring = false,
                    maxInstallments = 0
                };

                var orderResponse = await _vivaClient.CreatePaymentOrderAsync(orderRequest);

                // Process through PAX terminal with contactless enabled
                var paxResult = ProcessPAXContactlessTransaction(amount, orderResponse.orderCode.ToString());

                if (paxResult[0] == "000000")
                {
                    // Generate receipt with contactless indicator
                    var receiptData = new ReceiptData
                    {
                        TransactionId = paxResult[1],
                        Amount = amount,
                        Currency = currency,
                        AuthorizationCode = VivaWalletConfig.TestAuthCode,
                        MaskedCardNumber = "****-****-****-1234",
                        Timestamp = DateTime.Now,
                        MerchantId = VivaWalletConfig.MerchantId,
                        TerminalId = VivaWalletConfig.TerminalId,
                        Function101_3Enabled = VivaWalletConfig.EnableFunction101_3,
                        PaymentMethod = "Contactless Card",
                        IsContactless = true
                    };

                    var receiptGenerator = new ReceiptGenerator();
                    receiptGenerator.PrintReceipt(receiptData);
                }

                return paxResult;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Contactless payment error: {ex.Message}");
                return new string[] { "999996", $"Contactless payment error: {ex.Message}" };
            }
        }

        /// <summary>
        /// Process transaction through PAX terminal with contactless support
        /// </summary>
        private static string[] ProcessPAXContactlessTransaction(decimal amount, string orderCode)
        {
            try
            {
                // Use existing PAX integration but with contactless enabled
                PosLink poslink = new PosLink();
                PaymentRequest paymentReq = new PaymentRequest();

                // Configure communication settings
                poslink.CommSetting = GetVivaWalletCommSettings();

                // Configure payment request for contactless
                paymentReq.TenderType = 1; // Card payment
                paymentReq.TransType = 1;  // Sale transaction
                paymentReq.Amount = amount.ToString("F2");
                paymentReq.ECRRefNum = orderCode;

                // Enable contactless if supported
                if (VivaWalletConfig.EnableContactless)
                {
                    // Set contactless-specific parameters
                    paymentReq.ECRRefNum = "CONTACTLESS";
                }

                poslink.PaymentRequest = paymentReq;

                // Process transaction
                CommonPayment.isTransactionProcessing = true;
                poslink.ProcessTrans();
                PaymentResponse paymentRes = poslink.PaymentResponse;
                CommonPayment.isTransactionProcessing = false;

                Console.WriteLine($"PAX Response Code: {paymentRes.ResultCode}");
                Console.WriteLine($"PAX Response Text: {paymentRes.ResultTxt}");

                if (paymentRes.ResultCode == "000000")
                {
                    return new string[] { paymentRes.ResultCode, paymentRes.HostCode };
                }
                else
                {
                    return new string[] { paymentRes.ResultCode, paymentRes.ResultTxt };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PAX transaction error: {ex.Message}");
                return new string[] { "999995", $"PAX transaction error: {ex.Message}" };
            }
        }

        /// <summary>
        /// Get communication settings configured for Viva Wallet
        /// </summary>
        private static CommSetting GetVivaWalletCommSettings()
        {
            CommSetting commSetting = new CommSetting();
            
            commSetting.CommType = "HTTP";
            commSetting.DestIP = VivaWalletConfig.TerminalIP;
            commSetting.DestPort = VivaWalletConfig.TerminalPort;
            commSetting.TimeOut = VivaWalletConfig.Timeout.ToString();
            
            // Save settings
            commSetting.saveFile();
            
            return commSetting;
        }

        /// <summary>
        /// Enhanced transaction method that supports both manual and contactless payments
        /// </summary>
        public static async Task<string[]> ProcessTransactionAsync(int transType, string labelMessage, 
            string authId = "0", string amount = "0", string newStatus = "", bool useContactless = false)
        {
            try
            {
                decimal amountDecimal = 0;
                if (!string.IsNullOrEmpty(amount) && amount != "0")
                {
                    decimal.TryParse(amount.Trim('$'), out amountDecimal);
                }

                // For new transactions (type 1 = Auth/Sale)
                if (transType == 1)
                {
                    if (useContactless)
                    {
                        return await ProcessContactlessPaymentAsync(amountDecimal);
                    }
                    else
                    {
                        // Show option dialog for payment method
                        var paymentMethod = ShowPaymentMethodDialog();
                        
                        switch (paymentMethod)
                        {
                            case PaymentMethodChoice.Manual:
                                return await ProcessManualPaymentAsync(amountDecimal);
                            case PaymentMethodChoice.Contactless:
                                return await ProcessContactlessPaymentAsync(amountDecimal);
                            case PaymentMethodChoice.Traditional:
                                // Fall back to original PAX processing
                                return CommonPayment.transaction(transType, labelMessage, authId, amount, newStatus);
                            default:
                                return new string[] { "999999", "Payment cancelled" };
                        }
                    }
                }
                else
                {
                    // For other transaction types (capture, void, etc.), use original method
                    return CommonPayment.transaction(transType, labelMessage, authId, amount, newStatus);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Transaction processing error: {ex.Message}");
                return new string[] { "999994", $"Transaction error: {ex.Message}" };
            }
        }

        /// <summary>
        /// Show dialog to select payment method
        /// </summary>
        private static PaymentMethodChoice ShowPaymentMethodDialog()
        {
            var result = MessageBox.Show(
                "Choose payment method:\n\nYes = Manual Entry\nNo = Contactless\nCancel = Traditional PAX", 
                "Payment Method", 
                MessageBoxButtons.YesNoCancel, 
                MessageBoxIcon.Question);

            switch (result)
            {
                case DialogResult.Yes:
                    return PaymentMethodChoice.Manual;
                case DialogResult.No:
                    return PaymentMethodChoice.Contactless;
                case DialogResult.Cancel:
                    return PaymentMethodChoice.Traditional;
                default:
                    return PaymentMethodChoice.Cancelled;
            }
        }

        /// <summary>
        /// Cleanup resources
        /// </summary>
        public static void Dispose()
        {
            _vivaClient?.Dispose();
            _isInitialized = false;
        }
    }

    /// <summary>
    /// Payment method choices
    /// </summary>
    public enum PaymentMethodChoice
    {
        Manual,
        Contactless,
        Traditional,
        Cancelled
    }
}
