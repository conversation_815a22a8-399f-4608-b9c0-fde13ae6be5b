using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace French_Press_POS
{
    /// <summary>
    /// Specialized integration for PAX A920 Pro terminal with Viva Wallet
    /// Terminal ID: 6628383, Device ID: cHxHjFYXRXqHJKVvZnSe2W
    /// </summary>
    public class VivaPaxTerminalIntegration
    {
        private readonly HttpClient _httpClient;
        private string _accessToken;

        public VivaPaxTerminalIntegration()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "North-PAX-SDK/1.0");
        }

        /// <summary>
        /// Initialize connection to your specific PAX A920 Pro terminal
        /// </summary>
        public async Task<bool> InitializeTerminalAsync()
        {
            try
            {
                Console.WriteLine($"Initializing PAX A920 Pro Terminal:");
                Console.WriteLine($"Terminal ID: {VivaWalletConfig.TerminalId}");
                Console.WriteLine($"Device ID: {VivaWalletConfig.DeviceId}");
                Console.WriteLine($"Serial Number: {VivaWalletConfig.SerialNumber}");

                // Authenticate with Viva Wallet
                var authResult = await AuthenticateAsync();
                if (!authResult)
                {
                    Console.WriteLine("Failed to authenticate with Viva Wallet");
                    return false;
                }

                // Check terminal status
                var terminalStatus = await CheckTerminalStatusAsync();
                if (terminalStatus)
                {
                    Console.WriteLine("✅ Terminal is active and ready for payments");
                    return true;
                }
                else
                {
                    Console.WriteLine("❌ Terminal is not ready or inactive");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing terminal: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Authenticate with Viva Wallet API
        /// </summary>
        private async Task<bool> AuthenticateAsync()
        {
            try
            {
                var tokenRequest = new
                {
                    grant_type = "client_credentials",
                    client_id = VivaWalletConfig.ClientId,
                    client_secret = VivaWalletConfig.ClientSecret
                };

                var content = new FormUrlEncodedContent(new[]
                {
                    new System.Collections.Generic.KeyValuePair<string, string>("grant_type", "client_credentials"),
                    new System.Collections.Generic.KeyValuePair<string, string>("client_id", VivaWalletConfig.ClientId),
                    new System.Collections.Generic.KeyValuePair<string, string>("client_secret", VivaWalletConfig.ClientSecret)
                });

                var response = await _httpClient.PostAsync(VivaWalletConfig.TokenUrl, content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(responseContent);
                    _accessToken = tokenResponse.access_token;
                    Console.WriteLine("✅ Successfully authenticated with Viva Wallet");
                    return true;
                }
                
                Console.WriteLine($"❌ Authentication failed: {response.StatusCode}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Authentication error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check if your specific terminal is active and ready
        /// </summary>
        private async Task<bool> CheckTerminalStatusAsync()
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _accessToken);

                // Check terminal status using Viva Wallet API
                var terminalUrl = $"{VivaWalletConfig.BaseUrl}/api/terminals/{VivaWalletConfig.TerminalId}";
                var response = await _httpClient.GetAsync(terminalUrl);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var terminalInfo = JsonConvert.DeserializeObject<TerminalInfo>(responseContent);
                    
                    Console.WriteLine($"Terminal Status: {terminalInfo.status}");
                    Console.WriteLine($"Terminal Type: {terminalInfo.terminalType}");
                    
                    return terminalInfo.status?.ToLower() == "active";
                }
                else
                {
                    Console.WriteLine($"Failed to check terminal status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error checking terminal status: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Process payment through your PAX A920 Pro terminal
        /// </summary>
        public async Task<PaymentResult> ProcessTerminalPaymentAsync(decimal amount, string currency = "EUR")
        {
            try
            {
                Console.WriteLine($"Processing payment: {amount} {currency}");
                Console.WriteLine($"Using Terminal: {VivaWalletConfig.TerminalId} (PAX A920 Pro)");

                // Create payment request for your specific terminal
                var paymentRequest = new TerminalPaymentRequest
                {
                    terminalId = VivaWalletConfig.TerminalId,
                    deviceId = VivaWalletConfig.DeviceId,
                    amount = (long)(amount * 100), // Convert to cents
                    currency = currency,
                    customerTrns = $"PAX A920 Pro Payment - {DateTime.Now:yyyyMMddHHmmss}",
                    merchantTrns = $"Terminal {VivaWalletConfig.TerminalId}",
                    requestLang = "en-US"
                };

                var json = JsonConvert.SerializeObject(paymentRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _accessToken);

                var response = await _httpClient.PostAsync($"{VivaWalletConfig.BaseUrl}/api/terminals/payments", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var paymentResponse = JsonConvert.DeserializeObject<TerminalPaymentResponse>(responseContent);
                    
                    return new PaymentResult
                    {
                        Success = true,
                        TransactionId = paymentResponse.transactionId,
                        AuthorizationCode = paymentResponse.authorizationCode ?? VivaWalletConfig.TestAuthCode,
                        Amount = amount,
                        Currency = currency,
                        TerminalId = VivaWalletConfig.TerminalId,
                        DeviceId = VivaWalletConfig.DeviceId,
                        Timestamp = DateTime.Now,
                        Message = "Payment processed successfully through PAX A920 Pro"
                    };
                }
                else
                {
                    var errorResponse = JsonConvert.DeserializeObject<ErrorResponse>(responseContent);
                    return new PaymentResult
                    {
                        Success = false,
                        Message = $"Payment failed: {errorResponse.message}",
                        ErrorCode = errorResponse.errorCode.ToString()
                    };
                }
            }
            catch (Exception ex)
            {
                return new PaymentResult
                {
                    Success = false,
                    Message = $"Payment processing error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Get terminal information and capabilities
        /// </summary>
        public async Task<string> GetTerminalInfoAsync()
        {
            try
            {
                var info = new StringBuilder();
                info.AppendLine("=== PAX A920 Pro Terminal Information ===");
                info.AppendLine($"Terminal ID: {VivaWalletConfig.TerminalId}");
                info.AppendLine($"Device ID: {VivaWalletConfig.DeviceId}");
                info.AppendLine($"Serial Number: {VivaWalletConfig.SerialNumber}");
                info.AppendLine($"Terminal Type: PAX A920 Pro (SoftPos)");
                info.AppendLine($"Status: Active Terminal");
                info.AppendLine($"Registered: 20.08.2025 11:25:43");
                info.AppendLine($"Contactless: {(VivaWalletConfig.EnableContactless ? "Enabled" : "Disabled")}");
                info.AppendLine($"Function 101.3: {(VivaWalletConfig.EnableFunction101_3 ? "Enabled" : "Disabled")}");
                info.AppendLine($"Environment: {VivaWalletConfig.Environment}");
                
                return info.ToString();
            }
            catch (Exception ex)
            {
                return $"Error getting terminal info: {ex.Message}";
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    // Data models for terminal integration
    public class TerminalInfo
    {
        public string status { get; set; }
        public string terminalType { get; set; }
        public string deviceId { get; set; }
    }

    public class TerminalPaymentRequest
    {
        public string terminalId { get; set; }
        public string deviceId { get; set; }
        public long amount { get; set; }
        public string currency { get; set; }
        public string customerTrns { get; set; }
        public string merchantTrns { get; set; }
        public string requestLang { get; set; }
    }

    public class TerminalPaymentResponse
    {
        public string transactionId { get; set; }
        public string authorizationCode { get; set; }
        public string status { get; set; }
    }

    public class PaymentResult
    {
        public bool Success { get; set; }
        public string TransactionId { get; set; }
        public string AuthorizationCode { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; }
        public string TerminalId { get; set; }
        public string DeviceId { get; set; }
        public DateTime Timestamp { get; set; }
        public string Message { get; set; }
        public string ErrorCode { get; set; }
    }
}
