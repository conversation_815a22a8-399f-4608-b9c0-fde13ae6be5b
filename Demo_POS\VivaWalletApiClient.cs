using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace French_Press_POS
{
    /// <summary>
    /// HTTP client for Viva Wallet Payment API integration
    /// </summary>
    public class VivaWalletApiClient : IDisposable
    {
        private readonly HttpClient _httpClient;
        private string _accessToken;
        private DateTime _tokenExpiry;

        public VivaWalletApiClient()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "North-PAX-SDK/1.0");
        }

        /// <summary>
        /// Authenticate with Viva Wallet and get access token
        /// </summary>
        public async Task<bool> AuthenticateAsync()
        {
            try
            {
                var tokenRequest = new
                {
                    grant_type = "client_credentials",
                    client_id = VivaWalletConfig.ClientId,
                    client_secret = VivaWalletConfig.ClientSecret
                };

                var content = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("grant_type", "client_credentials"),
                    new KeyValuePair<string, string>("client_id", VivaWalletConfig.ClientId),
                    new KeyValuePair<string, string>("client_secret", VivaWalletConfig.ClientSecret)
                });

                var response = await _httpClient.PostAsync(VivaWalletConfig.TokenUrl, content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(responseContent);
                    
                    _accessToken = tokenResponse.access_token;
                    _tokenExpiry = DateTime.UtcNow.AddSeconds(tokenResponse.expires_in - 60); // 60 second buffer
                    
                    return true;
                }
                
                Console.WriteLine("Authentication failed: " + response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Authentication error: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Create a payment order with Viva Wallet
        /// </summary>
        public async Task<PaymentOrderResponse> CreatePaymentOrderAsync(PaymentOrderRequest request)
        {
            try
            {
                await EnsureValidTokenAsync();

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _accessToken);

                var response = await _httpClient.PostAsync(VivaWalletConfig.PaymentOrderUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    return JsonConvert.DeserializeObject<PaymentOrderResponse>(responseContent);
                }
                else
                {
                    var errorResponse = JsonConvert.DeserializeObject<ErrorResponse>(responseContent);
                    throw new VivaWalletException("Payment order creation failed: " + errorResponse.message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Payment order creation error: " + ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Process manual card entry payment
        /// </summary>
        public async Task<TransactionResponse> ProcessManualPaymentAsync(ManualPaymentRequest request)
        {
            try
            {
                await EnsureValidTokenAsync();

                // Create payment order first
                var orderRequest = new PaymentOrderRequest
                {
                    amount = (long)(request.Amount * 100), // Convert to cents
                    customerTrns = "Manual Payment - " + DateTime.Now.ToString("yyyyMMddHHmmss"),
                    customer = new Customer
                    {
                        email = "<EMAIL>",
                        fullName = "Manual Entry Customer"
                    },
                    paymentTimeout = 300,
                    preauth = false,
                    allowRecurring = false,
                    maxInstallments = 0
                };

                var orderResponse = await CreatePaymentOrderAsync(orderRequest);

                // Process the payment with card details
                var transactionRequest = new
                {
                    orderCode = orderResponse.orderCode,
                    cardNumber = request.CardNumber,
                    expiryDate = request.ExpiryDate,
                    cvv = request.CVV,
                    authorizationCode = request.AuthorizationCode
                };

                var json = JsonConvert.SerializeObject(transactionRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _accessToken);

                var response = await _httpClient.PostAsync(VivaWalletConfig.TransactionUrl + "/manual", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    return JsonConvert.DeserializeObject<TransactionResponse>(responseContent);
                }
                else
                {
                    var errorResponse = JsonConvert.DeserializeObject<ErrorResponse>(responseContent);
                    throw new VivaWalletException("Manual payment failed: " + errorResponse.message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Manual payment error: " + ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Ensure we have a valid access token
        /// </summary>
        private async Task EnsureValidTokenAsync()
        {
            if (string.IsNullOrEmpty(_accessToken) || DateTime.UtcNow >= _tokenExpiry)
            {
                await AuthenticateAsync();
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    // Data models for API requests/responses
    public class TokenResponse
    {
        public string access_token { get; set; }
        public string token_type { get; set; }
        public int expires_in { get; set; }
    }

    public class PaymentOrderRequest
    {
        public long amount { get; set; }
        public string customerTrns { get; set; }
        public Customer customer { get; set; }
        public int paymentTimeout { get; set; }
        public bool preauth { get; set; }
        public bool allowRecurring { get; set; }
        public int maxInstallments { get; set; }
    }

    public class Customer
    {
        public string email { get; set; }
        public string fullName { get; set; }
    }

    public class PaymentOrderResponse
    {
        public long orderCode { get; set; }
        public string status { get; set; }
        public string paymentUrl { get; set; }
    }

    public class ManualPaymentRequest
    {
        public decimal Amount { get; set; }
        public string CardNumber { get; set; }
        public string ExpiryDate { get; set; }
        public string CVV { get; set; }
        public string AuthorizationCode { get; set; }
    }

    public class TransactionResponse
    {
        public string transactionId { get; set; }
        public string status { get; set; }
        public string authorizationCode { get; set; }
        public decimal amount { get; set; }
        public string currency { get; set; }
        public DateTime timestamp { get; set; }
        public string maskedCardNumber { get; set; }
    }

    public class ErrorResponse
    {
        public string message { get; set; }
        public int errorCode { get; set; }
    }

    public class VivaWalletException : Exception
    {
        public VivaWalletException(string message) : base(message) { }
        public VivaWalletException(string message, Exception innerException) : base(message, innerException) { }
    }
}
