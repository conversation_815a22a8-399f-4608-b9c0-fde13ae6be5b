﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
    </startup>

    <appSettings>
        <!-- Viva Wallet Configuration - PRODUCTION -->
        <add key="VivaWallet.MerchantId" value="30481af3-63d9-42cd-93ea-1937a972b76d" />
        <add key="VivaWallet.TerminalId" value="6628383" />
        <add key="VivaWallet.DeviceId" value="cHxHjFYXRXqHJKVvZnSe2W" />
        <add key="VivaWallet.SerialNumber" value="2816bdb6488e0436" />
        <add key="VivaWallet.ApiKey" value="SothunZ2FxVRMkq666sbxbxB6VNbJG" />
        <add key="VivaWallet.ClientId" value="00pp9ggt8otvtzyfy3sv7y0d5u56oleukdkd7mma293z8.apps.vivapayments.com" />
        <add key="VivaWallet.ClientSecret" value="1E6Zd89B2S02a396KffVy9esrsVK5V" />
        <add key="VivaWallet.SourceCode" value="Default" />
        <add key="VivaWallet.Environment" value="Production" />
        <add key="VivaWallet.BaseUrl" value="https://api.vivapayments.com" />
        <add key="VivaWallet.AuthUrl" value="https://accounts.vivapayments.com" />
        <add key="VivaWallet.CheckoutUrl" value="https://www.vivapayments.com" />
        <add key="VivaWallet.Enabled" value="true" />

        <!-- PAX Terminal Configuration -->
        <add key="PAX.TerminalIP" value="**************" />
        <add key="PAX.TerminalPort" value="10009" />
        <add key="PAX.Timeout" value="60000" />
        <add key="PAX.EnableContactless" value="true" />
        <add key="PAX.EnableFunction101_3" value="true" />

        <!-- Payment Configuration -->
        <add key="Payment.DefaultCurrency" value="EUR" />
        <add key="Payment.SupportedCurrencies" value="EUR,USD" />
        <add key="Payment.TestAmount" value="1.00" />
        <add key="Payment.TestAuthCode" value="123789" />
    </appSettings>
</configuration>