{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"French_Press_POS/1.0.0": {"dependencies": {"FileHelpers": "3.5.2", "Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "9.0.0", "MoleQ.Integration.PaxWrapperPosLink": "*******", "PaxWrapperSDK": "1.0.0.1", "POSLink": "1.0.8704.21634"}, "runtime": {"French_Press_POS.dll": {}}}, "FileHelpers/3.5.2": {"runtime": {"lib/netstandard2.0/FileHelpers.dll": {"assemblyVersion": "3.5.2.0", "fileVersion": "3.5.2.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "System.Configuration.ConfigurationManager/9.0.0": {"dependencies": {"System.Diagnostics.EventLog": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}}, "System.Diagnostics.EventLog/9.0.0": {}, "System.Security.Cryptography.ProtectedData/9.0.0": {}, "MoleQ.Integration.PaxWrapperPosLink/*******": {"runtime": {"MoleQ.Integration.PaxWrapperPosLink.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "PaxWrapperSDK/1.0.0.1": {"runtime": {"PaxWrapperSDK.dll": {"assemblyVersion": "1.0.0.1", "fileVersion": "1.0.0.1"}}}, "POSLink/1.0.8704.21634": {"runtime": {"POSLink.dll": {"assemblyVersion": "1.0.8704.21634", "fileVersion": "1.12.0.0"}}}, "NLog/5.0.0.0": {"runtime": {"NLog.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.3.4.2778"}}}, "MoleQ.Ming.Core/*******": {"runtime": {"MoleQ.Ming.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"French_Press_POS/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FileHelpers/3.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-Z0jwjF4AmDBzRWfHFMdrcuBgmoQ2ChLaTmhRjnmReoUPUkT/+5StzWCB0N0RpslYeXiSyvcntcZNLtY58todFw==", "path": "filehelpers/3.5.2", "hashPath": "filehelpers.3.5.2.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PdkuMrwDhXoKFo/JxISIi9E8L+QGn9Iquj2OKDWHB6Y/HnUOuBouF7uS3R4Hw3FoNmwwMo6hWgazQdyHIIs27A==", "path": "system.configuration.configurationmanager/9.0.0", "hashPath": "system.configuration.configurationmanager.9.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qd01+AqPhbAG14KtdtIqFk+cxHQFZ/oqRSCoxU1F+Q6Kv0cl726sl7RzU9yLFGd4BUOKdN4XojXF0pQf/R6YeA==", "path": "system.diagnostics.eventlog/9.0.0", "hashPath": "system.diagnostics.eventlog.9.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>J<PERSON>+x/F6fmRQ7N6K8paasTw9PDZp4t7G76UjGNlSDgoHPF0h08vTzLYbLZpOLEJSg35d5wy2jCXGo84EN05DpQ==", "path": "system.security.cryptography.protecteddata/9.0.0", "hashPath": "system.security.cryptography.protecteddata.9.0.0.nupkg.sha512"}, "MoleQ.Integration.PaxWrapperPosLink/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "PaxWrapperSDK/1.0.0.1": {"type": "reference", "serviceable": false, "sha512": ""}, "POSLink/1.0.8704.21634": {"type": "reference", "serviceable": false, "sha512": ""}, "NLog/5.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "MoleQ.Ming.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}