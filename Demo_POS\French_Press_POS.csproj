<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C7C7842C-76EF-41F4-B94E-3F99E670D09A}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>French_Press_POS</RootNamespace>
    <AssemblyName>French_Press_POS</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>false</Deterministic>
    <!-- Versioning Configuration -->
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.$([System.DateTime]::Now.ToString("yyMM")).$([System.DateTime]::Now.ToString("ddHH"))</FileVersion>
    <AssemblyInformationalVersion>1.0.0-dev-$([System.DateTime]::Now.ToString("yyyyMMdd-HHmmss"))</AssemblyInformationalVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <!-- Debug-specific versioning -->
    <AssemblyInformationalVersion>1.0.0-debug-$([System.DateTime]::Now.ToString("yyyyMMdd-HHmmss"))</AssemblyInformationalVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <!-- Release-specific versioning -->
    <AssemblyInformationalVersion>1.0.0-release-$([System.DateTime]::Now.ToString("yyyyMMdd-HHmmss"))</AssemblyInformationalVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="FileHelpers, Version=3.5.2.0, Culture=neutral, PublicKeyToken=3e0c08d59cc3d657, processorArchitecture=MSIL">
      <HintPath>packages\FileHelpers.3.5.2\lib\net45\FileHelpers.dll</HintPath>
    </Reference>
    <Reference Include="netstandard" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="POSLink">
      <HintPath>libs\POSLink.dll</HintPath>
    </Reference>
    <Reference Include="POSLink2, Version=1.7.0.0, Culture=neutral, PublicKeyToken=dbf86e1b60761a1e, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>libs\POSLink2.dll</HintPath>
    </Reference>
    <Reference Include="PaxWrapperSDK">
      <HintPath>libs\PaxWrapperSDK.dll</HintPath>
    </Reference>
    <Reference Include="MoleQ.Integration.PaxWrapperPosLink">
      <HintPath>libs\MoleQ.Integration.PaxWrapperPosLink.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CommonPayment.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Provider.cs" />
    <None Include="Form11.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Form1.resx</DependentUpon>
    </None>
    <Compile Include="Form2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form2.Designer.cs">
      <DependentUpon>Form2.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <None Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Form11.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <EmbeddedResource Include="Form2.resx">
      <DependentUpon>Form2.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="App_Data\orders.csv" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\french-press-pos.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>