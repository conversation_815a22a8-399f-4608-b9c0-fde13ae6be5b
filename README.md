# Accept In-Person Payments with a .NET POS and North’s PAX SI SDK

This is a C#/.NET Point of Sale application that demonstrates a Card Present payment integration with [North's PAX Semi-Integrated SDK](https://developer.north.com/products/in-person/semi-integrated/pax-si-sdk). The payment terminal communicates sensitive PCI transaction information directly to the payment processor, EPX, which keeps that data out of the merchant's and ISV's systems, reducing your PCI responsibility. The following diagram provides an overview of the data flow with this solution:
<br><br><br>

![Semi-Integration diagram](https://github.com/PaymentsHubDevelopers/PaymentsHub-.NET-PAX-SI-SDK/assets/*********/0325116f-421b-4a39-b5ce-684c36678a27)

## Get Started

To get started, create a free [North Developer Portal account](https://developer.north.com/register) and follow the steps in the Setup section of the [Integration Guide](https://developer.north.com/products/in-person/semi-integrated/pax-si-sdk/integration-guide). [Contact](https://developer.north.com/contact) the North Sales Engineering team for support with your integration.

## Follow Along with the Tutorial

When you're ready to start building your app, you can follow along with [this tutorial](https://developer.north.com/blog/tutorial-csharp-dotnet-payment-application) for step-by-step instructions.

## Completed App

Your completed Point of Sale app with integrated payment functionality will look similar to the following:

![demo-pos-form2](https://github.com/PaymentsHubDevelopers/PaymentsHub-.NET-PAX-SI-SDK/assets/*********/5bbbf1b2-2018-47b9-85a1-49e5bcfd4c74)

