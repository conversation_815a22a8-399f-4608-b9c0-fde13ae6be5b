using System;
using System.Configuration;

namespace French_Press_POS
{
    /// <summary>
    /// Configuration management for Viva Wallet integration
    /// </summary>
    public static class VivaWalletConfig
    {
        // Viva Wallet API Configuration
        public static string MerchantId { get { return GetConfigValue("VivaWallet.MerchantId"); } }
        public static string TerminalId { get { return GetConfigValue("VivaWallet.TerminalId"); } }
        public static string DeviceId { get { return GetConfigValue("VivaWallet.DeviceId"); } }
        public static string SerialNumber { get { return GetConfigValue("VivaWallet.SerialNumber"); } }
        public static string ApiKey { get { return GetConfigValue("VivaWallet.ApiKey"); } }
        public static string ClientId { get { return GetConfigValue("VivaWallet.ClientId"); } }
        public static string ClientSecret { get { return GetConfigValue("VivaWallet.ClientSecret"); } }
        public static string SourceCode { get { return GetConfigValue("VivaWallet.SourceCode", "Default"); } }
        public static string Environment { get { return GetConfigValue("VivaWallet.Environment", "Production"); } }
        public static string BaseUrl { get { return GetConfigValue("VivaWallet.BaseUrl", "https://api.vivapayments.com"); } }
        public static string AuthUrl { get { return GetConfigValue("VivaWallet.AuthUrl", "https://accounts.vivapayments.com"); } }
        public static string CheckoutUrl { get { return GetConfigValue("VivaWallet.CheckoutUrl", "https://www.vivapayments.com"); } }
        public static bool Enabled { get { return bool.Parse(GetConfigValue("VivaWallet.Enabled", "true")); } }

        // PAX Terminal Configuration
        public static string TerminalIP { get { return GetConfigValue("PAX.TerminalIP", "**************"); } }
        public static string TerminalPort { get { return GetConfigValue("PAX.TerminalPort", "10009"); } }
        public static int Timeout { get { return int.Parse(GetConfigValue("PAX.Timeout", "60000")); } }
        public static bool EnableContactless { get { return bool.Parse(GetConfigValue("PAX.EnableContactless", "true")); } }
        public static bool EnableFunction101_3 { get { return bool.Parse(GetConfigValue("PAX.EnableFunction101_3", "true")); } }

        // Payment Configuration
        public static string DefaultCurrency { get { return GetConfigValue("Payment.DefaultCurrency", "EUR"); } }
        public static string[] SupportedCurrencies { get { return GetConfigValue("Payment.SupportedCurrencies", "EUR,USD").Split(','); } }
        public static decimal TestAmount { get { return decimal.Parse(GetConfigValue("Payment.TestAmount", "1.00")); } }
        public static string TestAuthCode { get { return GetConfigValue("Payment.TestAuthCode", "123789"); } }

        // API Endpoints
        public static string PaymentOrderUrl { get { return BaseUrl + "/api/orders"; } }
        public static string TokenUrl { get { return AuthUrl + "/connect/token"; } }
        public static string TransactionUrl { get { return BaseUrl + "/api/transactions"; } }

        /// <summary>
        /// Get configuration value with optional default
        /// </summary>
        private static string GetConfigValue(string key, string defaultValue = null)
        {
            try
            {
                var value = ConfigurationManager.AppSettings[key];
                return string.IsNullOrEmpty(value) ? defaultValue : value;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error reading config key '" + key + "': " + ex.Message);
                return defaultValue;
            }
        }

        /// <summary>
        /// Validate that all required configuration values are present
        /// </summary>
        public static bool ValidateConfig()
        {
            if (!Enabled)
            {
                Console.WriteLine("Viva Wallet integration is disabled");
                return false;
            }

            var requiredKeys = new[]
            {
                "VivaWallet.MerchantId",
                "VivaWallet.TerminalId",
                "VivaWallet.ApiKey",
                "VivaWallet.ClientId",
                "VivaWallet.ClientSecret"
            };

            foreach (var key in requiredKeys)
            {
                var value = GetConfigValue(key);
                if (string.IsNullOrEmpty(value) || value.StartsWith("YOUR_"))
                {
                    Console.WriteLine("Missing or invalid configuration for: " + key);
                    return false;
                }
            }

            Console.WriteLine("✅ Viva Wallet Configuration Validated:");
            Console.WriteLine("   Merchant ID: " + MerchantId);
            Console.WriteLine("   Terminal ID: " + TerminalId);
            Console.WriteLine("   Environment: " + Environment);
            Console.WriteLine("   Base URL: " + BaseUrl);

            return true;
        }

        /// <summary>
        /// Get authorization header for Viva Wallet API calls
        /// </summary>
        public static string GetBasicAuthHeader()
        {
            var credentials = Convert.ToBase64String(
                System.Text.Encoding.ASCII.GetBytes(MerchantId + ":" + ApiKey)
            );
            return "Basic " + credentials;
        }

        /// <summary>
        /// Check if we're in demo/test environment
        /// </summary>
        public static bool IsDemoEnvironment { get { return Environment.Equals("Demo", StringComparison.OrdinalIgnoreCase); } }
    }
}
