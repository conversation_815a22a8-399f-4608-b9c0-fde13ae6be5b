<?xml version="1.0"?>
<doc>
    <assembly>
        <name>FileHelpers</name>
    </assembly>
    <members>
        <member name="T:FileHelpers.FileHelperAsyncEngine">
            <summary>
            Async engine,  reads records from file in background,
            returns them record by record in foreground
            </summary>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine.#ctor(System.Type)">
            <summary>
			 Initializes a new instance of the FileHelperAsyncEngine class with the specified type of records.
		</summary><param name="recordType">Type of object to be handled.</param>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine.#ctor(System.Type,System.Text.Encoding)">
            <summary>
			 Initializes a new instance of the FileHelperAsyncEngine class with the specified type of records.
		</summary><param name="recordType">Type of object to be handled.</param>
            <param name="encoding">The encoding used by the Engine.</param>
        </member>
        <member name="T:FileHelpers.FileHelperAsyncEngine`1">
            <summary>
			<para><b>One of the main classes of the library.</b></para>
			<para>This engine is responsible to Read/Write the records <b>One by One</b> from/to files or streams.</para>
		</summary><remarks>
		 <para>You can set the <see cref="P:FileHelpers.ErrorManager.ErrorMode" /> of this class to find
			 out when there is an error. Retrieve them with the <see cref="P:FileHelpers.ErrorManager.Errors" />
			 property.</para>
		 <para>See in the <a href="http://www.filehelpers.net/diagrams/">Class Diagram</a> and in the
			<a href="http://www.filehelpers.net/quickstart/">Quick Start Guide</a> for more Info.</para>
		 <para>Or you can browse the <a href="http://www.filehelpers.net/examples/">Examples Section</a> for more code.</para>
		</remarks><seealso href="http://www.filehelpers.net/quickstart/">Quick Start Guide</seealso><seealso href="http://www.filehelpers.net/diagrams/">Class Diagram</seealso><seealso href="http://www.filehelpers.net/examples/">Examples of Use</seealso><seealso href="http://www.filehelpers.net/attributes/">Attributes List</seealso>
            <!-- Failed to insert some or all of included XML --><include file="Examples.xml" path="doc/examples/FileHelperAsyncEngine/*" />
            <typeparam name="T">The record type.</typeparam>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.#ctor">
            <summary>
			 Initializes a new instance of the FileHelperAsyncEngine class with the specified type of records.
		</summary>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.#ctor(System.Type)">
            <summary>
			 Initializes a new instance of the FileHelperAsyncEngine class with the specified type of records.
		</summary><param name="recordType">Type of object to be handled.</param>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.#ctor(System.Text.Encoding)">
            <summary>
			 Initializes a new instance of the FileHelperAsyncEngine class with the specified type of records.
		</summary>
            <param name="encoding">The encoding used by the Engine.</param>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.#ctor(System.Type,System.Text.Encoding)">
            <summary>
			 Initializes a new instance of the FileHelperAsyncEngine class with the specified type of records.
		</summary>
            <param name="encoding">The encoding used by the Engine.</param>
            <param name="recordType">Type of record to read</param>
        </member>
        <member name="P:FileHelpers.FileHelperAsyncEngine`1.LastRecord">
            <summary>Contains the last Record read by the <see cref="M:FileHelpers.FileHelperAsyncEngine`1.ReadNext" /> method.</summary><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
        </member>
        <member name="P:FileHelpers.FileHelperAsyncEngine`1.LastRecordValues">
            <summary>
            An array with the values of each field of the current record
            </summary>
        </member>
        <member name="P:FileHelpers.FileHelperAsyncEngine`1.Item(System.Int32)">
            <summary>
            Get a field value of the current records.
            </summary>
            <param name="fieldIndex" >The index of the field.</param>
        </member>
        <member name="P:FileHelpers.FileHelperAsyncEngine`1.Item(System.String)">
            <summary>
            Get a field value of the current records.
            </summary>
            <param name="fieldName" >The name of the field (case sensitive)</param>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.BeginReadStream(System.IO.TextReader)">
            <summary>
		Open a specified stream and seek to the first record.
		</summary><remarks>
		<para>This method only seeks to the first record.</para>
		<para>To read record by record use <b><see cref="M:FileHelpers.FileHelperAsyncEngine`1.ReadNext" /></b> method.</para>
		<para>When you stop reading the file you must call <b><see cref="M:FileHelpers.FileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="reader">The TextReader of the stream.</param><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.BeginReadFile(System.String)">
            <summary>
		Open a specified file and seek to the first record.
		</summary><remarks>
		<para>This method only opens the file.</para>
		<para>To read record by record use the <b><see cref="M:FileHelpers.FileHelperAsyncEngine`1.ReadNext" /></b> method.</para>
		<para>When you stop reading the file you must call <b><see cref="M:FileHelpers.FileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="fileName">The file path to be read.</param><returns>True if the file is successfully opened. False otherwise</returns><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.BeginReadFile(System.String,System.Int32)">
            <summary>
		Open a specified file and seek to the first record.
		</summary><remarks>
		<para>This method only opens the file.</para>
		<para>To read record by record use the <b><see cref="M:FileHelpers.FileHelperAsyncEngine`1.ReadNext" /></b> method.</para>
		<para>When you stop reading the file you must call <b><see cref="M:FileHelpers.FileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="fileName">The file path to be read.</param><returns>True if the file is successfully opened. False otherwise</returns><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
            <param name="bufferSize">Buffer size to read</param>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.BeginReadString(System.String)">
            <!-- No matching elements were found for the following include tag --><include file="FileHelperAsyncEngine.docs.xml" path="doc/BeginReadString/*" />
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.ReadNext">
            <summary>
			Reads the next record of a file.
    </summary><remarks>
		<para>This method returns the current record and moves to the next.</para>
		<para>If the end of file is reached this method return <b>null</b>.</para>
  	    </remarks><returns>The current record of the opened file.</returns><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.ReadToEnd">
            <summary>
            Return array of object for all data to end of the file
            </summary>
            <returns>Array of objects created from data on file</returns>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.ReadNexts(System.Int32)">
            <summary>
			Reads the specified number of records from a file or stream opened before.
		</summary><remarks>
		If there are less records than requested in the source, the file will be read to the end.
  	    </remarks><param name="numberOfRecords">
		    The number of records to read. If there are less records than requested in the
		    source, the file will be read to the end.
	    </param><returns>The next records of the opened file or stream.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.Flush">
            <summary>
            Save all the buffered data for write to the disk. 
            Useful to opened async engines that wants to save pending values to
            disk or for engines used for logging.
            </summary>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.Close">
            <summary>
			Close all opened stream readers and writers (if any).
		</summary><remarks>
		<para>This method must be called when you finish processing a file to dispose of the opened streams.</para>
  	    </remarks><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.BeginWriteStream(System.IO.TextWriter)">
            <summary>
		  Set the stream to be used in the <see cref="M:FileHelpers.FileHelperAsyncEngine`1.WriteNext(`0)" /> operation.
		</summary><remarks>
		<para>When you finished writing to the file you must call 
				the <b><see cref="M:FileHelpers.FileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="writer">To stream to writes to.</param><include file="Examples.xml" path="doc/examples/WriteAsync/*" />
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.BeginWriteFile(System.String)">
            <summary>
			Open a file to write it.
			If the file exists the engine will over write it
		</summary><remarks>
			<para>When you finish writing to the file you must call
			       the <b><see cref="M:FileHelpers.FileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="fileName">The file path to be opened for write.</param><include file="Examples.xml" path="doc/examples/WriteAsync/*" />
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.BeginWriteFile(System.String,System.Int32)">
            <summary>
			Open a file to write it.
			If the file exists the engine will over write it
		</summary><remarks>
			<para>When you finish writing to the file you must call
			       the <b><see cref="M:FileHelpers.FileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="fileName">The file path to be opened for write.</param><include file="Examples.xml" path="doc/examples/WriteAsync/*" />
            <param name="bufferSize">Size of the write buffer</param>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.BeginAppendToFile(System.String)">
            <summary>
            Begin the append to an existing file
            </summary>
            <param name="fileName">Filename to append to</param>
            <returns>Object to append  TODO:  ???</returns>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.BeginAppendToFile(System.String,System.Int32)">
            <summary>
		Open a file to Append to the end.
		</summary><remarks>
		<para>This method open and seek ends the file.</para>
		<para>When you finish appending to the file you must call <b><see cref="M:FileHelpers.FileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="fileName">The file path to be opened to write at the end.</param>
            <param name="bufferSize">Size of the buffer for writing</param>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.WriteNext(`0)">
            <summary>
			Write the next record to a file or stream opened.
		</summary><param name="record">The record to write.</param><include file="Examples.xml" path="doc/examples/WriteAsync/*" />
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.WriteNexts(System.Collections.Generic.IEnumerable{`0})">
            <summary>
			Write the next records to a file or stream opened.
		</summary><param name="records">The records to write (Can be an array, ArrayList, etc)</param>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.WriteNextValues">
            <summary>
            Write the current record values in the buffer. You can use
            engine[0] or engine["YourField"] to set the values.
            </summary>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
            <summary>Allows to loop record by record in the engine</summary>
            <returns>The enumerator</returns>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            
            <returns>
            An <see cref="T:System.Collections.IEnumerator"></see> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.System#IDisposable#Dispose">
            <summary>Release Resources</summary>
        </member>
        <member name="M:FileHelpers.FileHelperAsyncEngine`1.Finalize">
            <summary>Destructor</summary>
        </member>
        <member name="P:FileHelpers.FileHelperAsyncEngine`1.State">
            <summary>
            Indicates the current state of the engine.
            </summary>
        </member>
        <member name="T:FileHelpers.FileHelperAsyncEngine`1.EngineState">
            <summary>
            Indicates the State of an engine
            </summary>
        </member>
        <member name="F:FileHelpers.FileHelperAsyncEngine`1.EngineState.Closed">
            <summary>The Engine is closed</summary>
        </member>
        <member name="F:FileHelpers.FileHelperAsyncEngine`1.EngineState.Reading">
            <summary>The Engine is reading a file, string or stream</summary>
        </member>
        <member name="F:FileHelpers.FileHelperAsyncEngine`1.EngineState.Writing">
            <summary>The Engine is writing a file, string or stream</summary>
        </member>
        <member name="T:FileHelpers.IFileHelperAsyncEngine`1">
            <summary>
            Interface for the FileHelper Async Engine
            </summary>
            <typeparam name="T">Type of record to be read</typeparam>
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.LastRecord">
            <summary>Contains the last Record read by the <see cref="M:FileHelpers.IFileHelperAsyncEngine`1.ReadNext" /> method.</summary><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.LastRecordValues">
            <summary>
            An array with the values of each field of the current record
            </summary>
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.Options">
            <summary>Allows to change some record layout options at runtime</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.LineNumber">
            <!-- Failed to insert some or all of included XML --><include file="FileHelperEngine.docs.xml" path="doc/LineNum/*" />
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.TotalRecords">
            <!-- Failed to insert some or all of included XML --><include file="FileHelperEngine.docs.xml" path="doc/TotalRecords/*" />
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.RecordType">
            <!-- Failed to insert some or all of included XML --><include file="FileHelperEngine.docs.xml" path="doc/RecordType/*" />
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.HeaderText">
            <summary>The read header in the last read operation. If any.</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.FooterText">
            <summary>The read footer in the last read operation. If any.</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.NewLineForWrite">
            <summary>Newline char or string to be used when engine writes records.</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.Encoding">
            <summary>
            The encoding to Read and Write the streams.
            Default is the system's current ANSI code page.
            </summary>
            <value>Default is the system's current ANSI code page.</value>
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.ErrorManager">
            <summary>This is a common class that manage the errors of the library.</summary>
            <remarks>You can, for example, get the errors, their number, Save them to a file, etc.</remarks>
        </member>
        <member name="P:FileHelpers.IFileHelperAsyncEngine`1.ErrorMode">
            <summary>
            Indicates the behavior of the engine when it found an error.
            (shortcut for ErrorManager.ErrorMode)
            </summary>
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.BeginReadStream(System.IO.TextReader)">
            <summary>
		Open a specified stream and seek to the first record.
		</summary><remarks>
		<para>This method only seeks to the first record.</para>
		<para>To read record by record use <b><see cref="M:FileHelpers.IFileHelperAsyncEngine`1.ReadNext" /></b> method.</para>
		<para>When you stop reading the file you must call <b><see cref="M:FileHelpers.IFileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="reader">The TextReader of the stream.</param><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.BeginReadFile(System.String)">
            <summary>
		Open a specified file and seek to the first record.
		</summary><remarks>
		<para>This method only opens the file.</para>
		<para>To read record by record use the <b><see cref="M:FileHelpers.IFileHelperAsyncEngine`1.ReadNext" /></b> method.</para>
		<para>When you stop reading the file you must call <b><see cref="M:FileHelpers.IFileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="fileName">The file path to be read.</param><returns>True if the file is successfully opened. False otherwise</returns><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.BeginReadString(System.String)">
            <!-- No matching elements were found for the following include tag --><include file="FileHelperAsyncEngine.docs.xml" path="doc/BeginReadString/*" />
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.ReadNext">
            <summary>
			Reads the next record of a file.
    </summary><remarks>
		<para>This method returns the current record and moves to the next.</para>
		<para>If the end of file is reached this method return <b>null</b>.</para>
  	    </remarks><returns>The current record of the opened file.</returns><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.ReadToEnd">
            <summary>
            Read the file to the end,  returning an array of records
            </summary>
            <returns>Records left to process</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.ReadNexts(System.Int32)">
            <summary>
			Reads the specified number of records from a file or stream opened before.
		</summary><remarks>
		If there are less records than requested in the source, the file will be read to the end.
  	    </remarks><param name="numberOfRecords">
		    The number of records to read. If there are less records than requested in the
		    source, the file will be read to the end.
	    </param><returns>The next records of the opened file or stream.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.Flush">
            <summary>
            Save all the buffered data for write to the disk. 
            Useful with opened async engines when you want to save pending values to disk.
            Expecially for engines used for logging.
            </summary>
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.Close">
            <summary>
			Close all opened stream readers and writers (if any).
		</summary><remarks>
		<para>This method must be called when you finish processing a file to dispose of the opened streams.</para>
  	    </remarks><include file="Examples.xml" path="doc/examples/ReadAsync/*" />
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.BeginWriteStream(System.IO.TextWriter)">
            <summary>
		  Set the stream to be used in the <see cref="M:FileHelpers.IFileHelperAsyncEngine`1.WriteNext(`0)" /> operation.
		</summary><remarks>
		<para>When you finished writing to the file you must call 
				the <b><see cref="M:FileHelpers.IFileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="writer">To stream to writes to.</param><include file="Examples.xml" path="doc/examples/WriteAsync/*" />
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.BeginWriteFile(System.String)">
            <summary>
			Open a file to write it.
			If the file exists the engine will over write it
		</summary><remarks>
			<para>When you finish writing to the file you must call
			       the <b><see cref="M:FileHelpers.IFileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="fileName">The file path to be opened for write.</param><include file="Examples.xml" path="doc/examples/WriteAsync/*" />
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.BeginAppendToFile(System.String)">
            <summary>
		Open a file to Append to the end.
		</summary><remarks>
		<para>This method open and seek ends the file.</para>
		<para>When you finish appending to the file you must call <b><see cref="M:FileHelpers.IFileHelperAsyncEngine`1.Close" /></b> method.</para>
		</remarks><param name="fileName">The file path to be opened to write at the end.</param>
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.WriteNext(`0)">
            <summary>
			Write the next record to a file or stream opened.
		</summary><param name="record">The record to write.</param><include file="Examples.xml" path="doc/examples/WriteAsync/*" />
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.WriteNexts(System.Collections.Generic.IEnumerable{`0})">
            <summary>
			Write the next records to a file or stream opened.
		</summary><param name="records">The records to write (Can be an array, ArrayList, etc)</param>
        </member>
        <member name="M:FileHelpers.IFileHelperAsyncEngine`1.WriteNextValues">
            <summary>
            Write the current record values in the buffer. You can use engine[0] or engine["YourField"] to set the values.
            </summary>
        </member>
        <member name="E:FileHelpers.IFileHelperAsyncEngine`1.BeforeReadRecord">
            <summary>Called in read operations just before the record string is translated to a record.</summary>
        </member>
        <member name="E:FileHelpers.IFileHelperAsyncEngine`1.AfterReadRecord">
            <summary>Called in read operations just after the record was created from a record string.</summary>
        </member>
        <member name="E:FileHelpers.IFileHelperAsyncEngine`1.BeforeWriteRecord">
            <summary>Called in write operations just before the record is converted to a string to write it.</summary>
        </member>
        <member name="E:FileHelpers.IFileHelperAsyncEngine`1.AfterWriteRecord">
            <summary>Called in write operations just after the record was converted to a string.</summary>
        </member>
        <member name="E:FileHelpers.IFileHelperAsyncEngine`1.Progress">
            <summary>Called to notify progress.</summary>
        </member>
        <member name="T:FileHelpers.ConditionalRecordAttribute">
            <summary>Allow to declaratively set what records must be included or excluded while reading.</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">Complete attributes list</a> for more information and examples of each one.</remarks>
            <example>
            [DelimitedRecord(",")] 
            [ConditionalRecord(RecordCondition.ExcludeIfBegins, "//")] 
            public class ConditionalType1 
            { 
            
            // Using Regular Expressions example
            [DelimitedRecord(",")]
            [ConditionalRecord(RecordCondition.IncludeIfMatchRegex, ".*abc??")]
            public class ConditionalType3
            { 
            </example>
        </member>
        <member name="P:FileHelpers.ConditionalRecordAttribute.Condition">
            <summary> The condition used to include or exclude each record </summary>
        </member>
        <member name="P:FileHelpers.ConditionalRecordAttribute.ConditionSelector">
            <summary> The selector (match string) for the condition. </summary>
            <remarks>The string will have a condition, included, excluded start with etc</remarks>
        </member>
        <member name="M:FileHelpers.ConditionalRecordAttribute.#ctor(FileHelpers.RecordCondition,System.String)">
            <summary>Allow to declaratively show what records must be included or excluded</summary>
            <param name="condition">The condition used to include or exclude each record <see cref="T:FileHelpers.RecordCondition"/>conditions</param>
            <param name="conditionSelector">The selector (match string) for the condition.</param>
        </member>
        <member name="M:FileHelpers.ConditionalRecordAttribute.CheckNullOrEmpty(System.String,System.String)">
            <summary>
            Check the string is null or empty and throw an exception
            </summary>
            <param name="val">value to test</param>
            <param name="paramName">name of parameter to check</param>
        </member>
        <member name="T:FileHelpers.ConverterKind">
            <summary>
            Indicates the Conversion used in the <see cref="T:FileHelpers.FieldConverterAttribute"/>.
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.None">
            <summary>Null Converter.</summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Date">
            <summary>
            <para>Convert from or to <b>Date</b> values.</para>
            <para>Params: arg1 is the <b>string</b> with the date format.</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Boolean">
            <summary>
            <para>Convert from or to <b>Boolean</b> values.</para>
            <para>Params: arg1 is the <b>TRUE</b> string</para>
            <para>Params: arg2 is the <b>FALSE</b> string</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Byte">
            <summary>
            <para>Convert from or to <b>Byte</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Int16">
            <summary>
            <para>Convert from or to <b>Int16 or short</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Int32">
            <summary>
            <para>Convert from or to <b>Int32 or int</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Int64">
            <summary>
            <para>Convert from or to <b>Int64 or long</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Decimal">
            <summary>
            <para>Convert from or to <b>Decimal</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Double">
            <summary>
            <para>Convert from or to <b>Double</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.PercentDouble">
            <summary>
            <para>Convert from or to <b>Double</b> values. Understands Percent '%' symbol 
            and if present returns number /100 only while reading</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Single">
            <summary>
            <para>Convert from or to <b>Single</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.SByte">
            <summary>
            <para>Convert from or to <b>Byte</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.UInt16">
            <summary>
            <para>Convert from or to <b>UInt16 or unsigned short</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.UInt32">
            <summary>
            <para>Convert from or to <b>UInt32 or unsigned int</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.UInt64">
            <summary>
            <para>Convert from or to <b>UInt64 or unsigned long</b> values.</para>
            <para>Params: arg1 is either a <b>decimal separator</b>, by default '.', or a culture name (eg. "en-US", "fr-FR")</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.DateMultiFormat">
            <summary>
            <para>Convert from or to <b>Date</b> values using more than one valid format.</para>
            <para>Params: arg1 is a <b>string</b> with the main date format. This format is the unique used for write.</para>
            <para>Params: arg2 is a <b>string</b> with another valid read format.</para>
            <para>Params: arg3 is a <b>string</b> with another valid read format.</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Char">
            <summary>
            <para>Convert from or to <b>Char</b> values.</para>
            <para>Params: arg1 is a <b>string</b> with "" for default behavior, "x" for make the char ToLower or "X" for make it ToUpper.</para>
            </summary>
        </member>
        <member name="F:FileHelpers.ConverterKind.Guid">
            <summary>
            <para>Convert from/to <b>Guid</b> values.</para>
            <para>Params: arg1 is a <b>string</b> with one of the Guid.ToString() formats: "N", "D", "B", or "P"</para>
            <para>   "N" ->  xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx </para>
            <para>   "D" ->  xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx </para>
            <para>   "B" ->  {xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx} </para>
            <para>   "P" ->  (xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx) </para>
            </summary>
        </member>
        <member name="T:FileHelpers.DelimitedRecordAttribute">
            <summary>Indicates that this class represents a delimited record. </summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.DelimitedRecordAttribute.Separator">
            <summary>The string used as a field separator.</summary>
        </member>
        <member name="M:FileHelpers.DelimitedRecordAttribute.#ctor(System.String,System.String)">
            <summary>Indicates that this class represents a delimited record. </summary>
            <param name="delimiter">The separator string used to split the fields of the record.</param>
            <param name="defaultCultureName">Default culture name used for each properties if no converter is specified otherwise. If null, the default decimal separator (".") will be used.</param>
        </member>
        <member name="T:FileHelpers.FieldAlignAttribute">
            <summary>Indicates the <see cref="T:FileHelpers.AlignMode"/> used for <b>write</b> operations.</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.FieldAlignAttribute.Align">
            <summary>The position of the alignment.</summary>
        </member>
        <member name="P:FileHelpers.FieldAlignAttribute.AlignChar">
            <summary>The character used to align.</summary>
        </member>
        <member name="M:FileHelpers.FieldAlignAttribute.#ctor(FileHelpers.AlignMode)">
            <summary>Uses the ' ' char to align.</summary>
            <param name="align">The position of the alignment.</param>
        </member>
        <member name="M:FileHelpers.FieldAlignAttribute.#ctor(FileHelpers.AlignMode,System.Char)">
            <summary>You can indicate the align character.</summary>
            <param name="align">The position of the alignment.</param>
            <param name="alignChar">The character used to align.</param>
        </member>
        <member name="T:FileHelpers.FieldArrayLengthAttribute">
            <summary>
            Allows you to set the length or bounds that the target array field must have.
            </summary>
        </member>
        <member name="M:FileHelpers.FieldArrayLengthAttribute.#ctor(System.Int32,System.Int32)">
            <summary>
            Allows you to set the bounds that the target array field must have.
            </summary>
            <param name="minLength">The lower bound</param>
            <param name="maxLength">The upper bound</param>
        </member>
        <member name="M:FileHelpers.FieldArrayLengthAttribute.#ctor(System.Int32)">
            <summary>
            Allow you to set the exact length that the target array field must have.
            </summary>
            <param name="length">The exact length of the array field.</param>
        </member>
        <member name="P:FileHelpers.FieldArrayLengthAttribute.MinLength">
            <summary>Array lower bound.</summary>
        </member>
        <member name="P:FileHelpers.FieldArrayLengthAttribute.MaxLength">
            <summary>Array upper bound.</summary>
        </member>
        <member name="T:FileHelpers.FieldAttribute">
            <summary>Base class of <see cref="T:FileHelpers.FieldFixedLengthAttribute"/> and <see cref="T:FileHelpers.FieldDelimiterAttribute"/></summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">Complete Attributes List</a> for more information and examples of each one.</remarks>
        </member>
        <member name="M:FileHelpers.FieldAttribute.#ctor">
            <summary>Abstract class, see the derived classes.</summary>
        </member>
        <member name="T:FileHelpers.FieldCaptionAttribute">
            <summary>Indicates a different caption for this field, which overrides FieldFriendlyName when calling GetFileHeader. </summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.FieldCaptionAttribute.Caption">
            <summary>
            Gets the caption for this field
            </summary>
        </member>
        <member name="M:FileHelpers.FieldCaptionAttribute.#ctor(System.String)">
            <summary>Indicates a different caption for this field. </summary>
            <param name="caption">The string used for the field in the header row.</param>
        </member>
        <member name="T:FileHelpers.FieldConverterAttribute">
            <summary>Indicates the <see cref="T:FileHelpers.ConverterKind"/> used for read/write operations.</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">Complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="M:FileHelpers.FieldConverterAttribute.#ctor(FileHelpers.ConverterKind)">
            <summary>Indicates the <see cref="T:FileHelpers.ConverterKind"/> used for read/write operations. </summary>
            <param name="converter">The <see cref="T:FileHelpers.ConverterKind"/> used for the transformations.</param>
        </member>
        <member name="M:FileHelpers.FieldConverterAttribute.#ctor(FileHelpers.ConverterKind,System.String)">
            <summary>Indicates the <see cref="T:FileHelpers.ConverterKind"/> used for read/write operations. </summary>
            <param name="converter">The <see cref="T:FileHelpers.ConverterKind"/> used for the transformations.</param>
            <param name="arg1">The first param passed directly to the Converter Constructor.</param>
        </member>
        <member name="M:FileHelpers.FieldConverterAttribute.#ctor(FileHelpers.ConverterKind,System.String,System.String)">
            <summary>Indicates the <see cref="T:FileHelpers.ConverterKind"/> used for read/write operations. </summary>
            <param name="converter">The <see cref="T:FileHelpers.ConverterKind"/> used for the transformations.</param>
            <param name="arg1">The first param passed directly to the Converter Constructor.</param>
            <param name="arg2">The second param passed directly to the Converter Constructor.</param>
        </member>
        <member name="M:FileHelpers.FieldConverterAttribute.#ctor(FileHelpers.ConverterKind,System.String,System.String,System.String)">
            <summary>Indicates the <see cref="T:FileHelpers.ConverterKind"/> used for read/write operations. </summary>
            <param name="converter">The <see cref="T:FileHelpers.ConverterKind"/> used for the transformations.</param>
            <param name="arg1">The first param passed directly to the Converter Constructor.</param>
            <param name="arg2">The second param passed directly to the Converter Constructor.</param>
            <param name="arg3">The third param passed directly to the Converter Constructor.</param>
        </member>
        <member name="M:FileHelpers.FieldConverterAttribute.#ctor(FileHelpers.ConverterKind,System.String[])">
            <summary>
            Indicates the <see cref="T:FileHelpers.ConverterKind"/> used for read/write operations. 
            </summary>
            <param name="converter">The <see cref="T:FileHelpers.ConverterKind"/> used for the transformations.</param>
            <param name="args">An array of parameters passed directly to the Converter</param>
        </member>
        <member name="M:FileHelpers.FieldConverterAttribute.#ctor(System.Type,System.String)">
            <summary>Indicates a custom <see cref="T:FileHelpers.ConverterBase"/> implementation.</summary>
            <param name="customConverter">The Type of your custom converter.</param>
            <param name="arg1">The first param passed directly to the Converter Constructor.</param>
        </member>
        <member name="M:FileHelpers.FieldConverterAttribute.#ctor(System.Type,System.String,System.String)">
            <summary>Indicates a custom <see cref="T:FileHelpers.ConverterBase"/> implementation.</summary>
            <param name="customConverter">The Type of your custom converter.</param>
            <param name="arg1">The first param passed directly to the Converter Constructor.</param>
            <param name="arg2">The second param passed directly to the Converter Constructor.</param>
        </member>
        <member name="M:FileHelpers.FieldConverterAttribute.#ctor(System.Type,System.String,System.String,System.String)">
            <summary>Indicates a custom <see cref="T:FileHelpers.ConverterBase"/> implementation.</summary>
            <param name="customConverter">The Type of your custom converter.</param>
            <param name="arg1">The first param passed directly to the Converter Constructor.</param>
            <param name="arg2">The second param passed directly to the Converter Constructor.</param>
            <param name="arg3">The third param passed directly to the Converter Constructor.</param>
        </member>
        <member name="M:FileHelpers.FieldConverterAttribute.#ctor(System.Type,System.Object[])">
            <summary>Indicates a custom <see cref="T:FileHelpers.ConverterBase"/> implementation.</summary>
            <param name="customConverter">The Type of your custom converter.</param>
            <param name="args">A list of params passed directly to your converter constructor.</param>
        </member>
        <member name="M:FileHelpers.FieldConverterAttribute.#ctor(System.Type)">
            <summary>Indicates a custom <see cref="T:FileHelpers.ConverterBase"/> implementation.</summary>
            <param name="customConverter">The Type of your custom converter.</param>
        </member>
        <member name="P:FileHelpers.FieldConverterAttribute.Converter">
            <summary>The final concrete converter used for FieldToString and StringToField operations </summary>
        </member>
        <member name="P:FileHelpers.FieldConverterAttribute.Kind">
            <summary>The <see cref="T:FileHelpers.ConverterKind"/> if a default converter is used </summary>
        </member>
        <member name="T:FileHelpers.FieldDelimiterAttribute">
            <summary>Indicates a different delimiter for this field. </summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.FieldDelimiterAttribute.Delimiter">
            <summary>
            Gets the Delimiter for this field
            </summary>
        </member>
        <member name="M:FileHelpers.FieldDelimiterAttribute.#ctor(System.String)">
            <summary>Indicates a different delimiter for this field. </summary>
            <param name="separator">The separator string used to split the fields of the record.</param>
        </member>
        <member name="T:FileHelpers.FieldFixedLengthAttribute">
            <summary>Indicates the length of a FixedLength field.</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.FieldFixedLengthAttribute.Length">
            <summary>Length of this Fixed Length field.</summary>
        </member>
        <member name="P:FileHelpers.FieldFixedLengthAttribute.OverflowMode">
            <summary>
            Overflow behavior for this Fixed Length field.
            By default uses <code>OverflowMode.DiscardEnd</code>.
            </summary>
        </member>
        <member name="M:FileHelpers.FieldFixedLengthAttribute.#ctor(System.Int32)">
            <summary>Indicates the length of a Fixed Length field.</summary>
            <param name="length">The length of the field.</param>
        </member>
        <member name="T:FileHelpers.FieldHiddenAttribute">
            <summary>Hides the field to the library, the library does not use the
            target field at all. Nor for read and write
            <para/>
            Note: If the field is in the record structure but you want to discard the values in that position use <see
            cref="T:FileHelpers.FieldValueDiscardedAttribute"/></summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="T:FileHelpers.FieldIgnoredAttribute">
            <summary>Hides the field to the library. Obsolete: You must use [FieldHidden]</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="T:FileHelpers.FieldInNewLineAttribute">
            <summary>
            Indicates the target field has a new line before this value 
            i.e. indicates that the records have multiple lines, 
            and this field is in the beginning of a line.
            </summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="M:FileHelpers.FieldInNewLineAttribute.#ctor">
            <summary>
            Indicates the target field has a new line before this value 
            i.e. indicates that the records have multiple lines, 
            and this field is in the beginning of a line.
            </summary>
        </member>
        <member name="T:FileHelpers.FieldNotEmptyAttribute">
            <summary>
            Indicates that the target field cannot contain an empty string value.
            This attribute is used for read.
            </summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="T:FileHelpers.FieldNotInFileAttribute">
            <summary>Hides the field to the library. Obsolete: You must use [FieldHidden]</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="T:FileHelpers.FieldNullValueAttribute">
            <summary>
            Indicates the value to assign to the field in the case of a NULL value.
            A default value if none supplied in the field itself.
            </summary>
            <remarks>
            You must specify a string and a converter that can be converted to the
            type or an object of the correct type to be directly assigned.
            <para/>
            See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more
            information and examples of each one.
            </remarks>
        </member>
        <member name="P:FileHelpers.FieldNullValueAttribute.NullValue">
            <summary>Default value for a null value.</summary>
        </member>
        <member name="M:FileHelpers.FieldNullValueAttribute.#ctor(System.Object)">
            <summary>
            Defines the default in event of a null value.
            Object must be of the correct type
            </summary>
            <param name="nullValue">The value to assign the case of a NULL value.</param>
        </member>
        <member name="M:FileHelpers.FieldNullValueAttribute.#ctor(System.Type,System.String)">
            <summary>Indicates a type and a string to be converted to that type.</summary>
            <param name="type">The type of the null value.</param>
            <param name="nullValue">The string to be converted to the specified type.</param>
        </member>
        <member name="T:FileHelpers.FieldOptionalAttribute">
            <summary>
            Indicates that the target field might be on the source file.
            If it is not present then the value will be null (TODO: Check null)
            This attribute is used for read.
            </summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="T:FileHelpers.FieldOrderAttribute">
            <summary>
            Indicates the relative order of the current field.
            Note: If you use this property for one field you
            must to use it for all fields.
            </summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.FieldOrderAttribute.Order">
            <summary>The relative position order of this field.</summary>
        </member>
        <member name="M:FileHelpers.FieldOrderAttribute.#ctor(System.Int32)">
            <summary>
            Indicates the relative order of the current field.
            Note:If you use this property for one field you must to use it for
            all fields.
            </summary>
            <param name="order">Indicates the relative order of the current field</param>
        </member>
        <member name="T:FileHelpers.FieldQuotedAttribute">
            <summary>
            Indicates that the field must be read and written as a Quoted String. 
            By default uses "" (double quotes)
            </summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.FieldQuotedAttribute.QuoteChar">
            <summary>The char used to quote the string.</summary>
        </member>
        <member name="P:FileHelpers.FieldQuotedAttribute.QuoteMode">
            <summary>Indicates if the handling of optionals in the quoted field.</summary>
        </member>
        <member name="P:FileHelpers.FieldQuotedAttribute.QuoteMultiline">
            <summary>Indicates if the field can span multiple lines.</summary>
        </member>
        <member name="M:FileHelpers.FieldQuotedAttribute.#ctor">
            <summary>
            Indicates that the field must be read and written as a Quoted String with double quotes.
            </summary>
        </member>
        <member name="M:FileHelpers.FieldQuotedAttribute.#ctor(System.Char)">
            <summary>
            Indicates that the field must be read and written as a Quoted String
            with the specified char.
            </summary>
            <param name="quoteChar">The char used to quote the string.</param>
        </member>
        <member name="M:FileHelpers.FieldQuotedAttribute.#ctor(FileHelpers.QuoteMode)">
            <summary>
            Indicates that the field must be read and written as a "Quoted String"
            (that can be optional depending of the mode).
            </summary>
            <param name="mode">Indicates if the handling of optionals in the quoted field.</param>
        </member>
        <member name="M:FileHelpers.FieldQuotedAttribute.#ctor(FileHelpers.QuoteMode,FileHelpers.MultilineMode)">
            <summary>Indicates that the field must be read and written as a Quoted String (that can be optional).</summary>
            <param name="mode">Indicates if the handling of optionals in the quoted field.</param>
            <param name="multiline">Indicates if the field can span multiple lines.</param>
        </member>
        <member name="M:FileHelpers.FieldQuotedAttribute.#ctor(System.Char,FileHelpers.QuoteMode)">
            <summary>Indicates that the field must be read and written as a Quoted String (that can be optional).</summary>
            <param name="quoteChar">The char used to quote the string.</param>
            <param name="mode">Indicates if the handling of optionals in the quoted field.</param>
        </member>
        <member name="M:FileHelpers.FieldQuotedAttribute.#ctor(System.Char,FileHelpers.QuoteMode,FileHelpers.MultilineMode)">
            <summary>Indicates that the field must be read and written as a Quoted String (that can be optional).</summary>
            <param name="quoteChar">The char used to quote the string.</param>
            <param name="mode">Indicates if the handling of optionals in the quoted field.</param>
            <param name="multiline">Indicates if the field can span multiple lines.</param>
        </member>
        <member name="M:FileHelpers.FieldQuotedAttribute.#ctor(FileHelpers.MultilineMode)">
            <summary>Indicates that the field must be read and written like a Quoted String with double quotes.</summary>
            <param name="multiline">Indicates if the field can span multiple lines.</param>
        </member>
        <member name="T:FileHelpers.FieldTrimAttribute">
            <summary>Indicates the <see cref="P:FileHelpers.FieldTrimAttribute.TrimMode"/> used after reading to truncate the field. </summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.FieldTrimAttribute.TrimChars">
            <summary>A string of chars used to trim.</summary>
        </member>
        <member name="P:FileHelpers.FieldTrimAttribute.TrimMode">
            <summary>The TrimMode used after read.</summary>
        </member>
        <member name="M:FileHelpers.FieldTrimAttribute.#ctor(FileHelpers.TrimMode)">
            <summary>Indicates the <see cref="P:FileHelpers.FieldTrimAttribute.TrimMode"/> used after read to truncate the field. By default trims the blank spaces and tabs.</summary>
            <param name="mode">The <see cref="P:FileHelpers.FieldTrimAttribute.TrimMode"/> used after read.</param>
        </member>
        <member name="M:FileHelpers.FieldTrimAttribute.#ctor(FileHelpers.TrimMode,System.Char[])">
            <summary>Indicates the <see cref="P:FileHelpers.FieldTrimAttribute.TrimMode"/> used after read to truncate the field. </summary>
            <param name="mode">The <see cref="P:FileHelpers.FieldTrimAttribute.TrimMode"/> used after read.</param>
            <param name="chars">A list of chars used to trim.</param>
        </member>
        <member name="M:FileHelpers.FieldTrimAttribute.#ctor(FileHelpers.TrimMode,System.String)">
            <summary>Indicates the <see cref="P:FileHelpers.FieldTrimAttribute.TrimMode"/> used after read to truncate the field. </summary>
            <param name="mode">The <see cref="P:FileHelpers.FieldTrimAttribute.TrimMode"/> used after read.</param>
            <param name="trimChars">A string of chars used to trim.</param>
        </member>
        <member name="T:FileHelpers.FieldValueDiscardedAttribute">
            <summary>Discards the values for the target field.
            Note: If the field <b>is not</b> on the source file you must use <see cref="T:FileHelpers.FieldHiddenAttribute"/></summary>
            <remarks>
            <para/>
            See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more
            information and examples of each one.
            </remarks>
        </member>
        <member name="T:FileHelpers.FixedLengthRecordAttribute">
            <summary>Indicates that this class represents a fixed length record.</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.FixedLengthRecordAttribute.FixedMode">
            <summary>Indicates the behavior when variable length records are found.</summary>
        </member>
        <member name="M:FileHelpers.FixedLengthRecordAttribute.#ctor">
            <summary>Indicates that this class represents a fixed length
            record. By default fixed length files require the records to have
            equal length.
            (ie the record length equals the sum of each field length.
            </summary>
        </member>
        <member name="M:FileHelpers.FixedLengthRecordAttribute.#ctor(FileHelpers.FixedMode,System.String)">
            <summary>
            Indicates that this class represents a fixed length record with the
            specified variable length record behavior.
            </summary>
            <param name="fixedMode">The <see cref="T:FileHelpers.FixedMode"/> used for variable length records. By Default is FixedMode.ExactLength</param>
            <param name="defaultCultureName">Default culture name used for each properties if no converter is specified otherwise. If null, the default decimal separator (".") will be used.</param>
        </member>
        <member name="T:FileHelpers.IgnoreCommentedLinesAttribute">
            <summary>Indicates that the engine must ignore commented lines while reading.</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.IgnoreCommentedLinesAttribute.CommentMarker">
            <summary>
            Comment marker string
            </summary>
        </member>
        <member name="P:FileHelpers.IgnoreCommentedLinesAttribute.AnyPlace">
            <summary>
            Can the comment marker have preceding spaces
            </summary>
        </member>
        <member name="M:FileHelpers.IgnoreCommentedLinesAttribute.#ctor(System.String)">
            <summary>Indicates that the engine will ignore commented lines while reading.
            (The Comment Marker can have any number of spaces or tabs to the left)</summary>
            <param name="commentMarker">The comment marker used to ignore the lines</param>
        </member>
        <member name="M:FileHelpers.IgnoreCommentedLinesAttribute.#ctor(System.String,System.Boolean)">
            <summary>Indicates that the engine will ignore commented lines while reading.</summary>
            <param name="commentMarker">The comment marker used to ignore the lines</param>
            <param name="anyPlace">Indicates if the comment can have spaces or tabs to the left (true by default)</param>
        </member>
        <member name="T:FileHelpers.IgnoreEmptyLinesAttribute">
            <summary>Indicates that the engine will ignore the empty lines while reading.</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.IgnoreEmptyLinesAttribute.IgnoreSpaces">
            <summary>Ignore lines consisting of only whitespace.</summary>
        </member>
        <member name="M:FileHelpers.IgnoreEmptyLinesAttribute.#ctor">
            <summary>Indicates that the engine will ignore the empty lines while reading.</summary>
        </member>
        <member name="M:FileHelpers.IgnoreEmptyLinesAttribute.#ctor(System.Boolean)">
            <summary>Indicates that the engine will ignore the empty lines while reading.</summary>
            <param name="ignoreSpaces">Ignore lines consisting of only whitespace.</param>
        </member>
        <member name="T:FileHelpers.IgnoreFirstAttribute">
            <summary>Indicates the number of lines at beginning of the file to be ignored.</summary>
            <remarks>
            Useful to ignore header records that you are not interested in.
            <para/>
            See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more
            information and examples of each one.
            </remarks>
        </member>
        <member name="P:FileHelpers.IgnoreFirstAttribute.NumberOfLines">
            <summary>The number of lines at beginning of the file to be ignored.</summary>
        </member>
        <member name="M:FileHelpers.IgnoreFirstAttribute.#ctor">
            <summary>Indicates that the first line of the file is ignored.</summary>
        </member>
        <member name="M:FileHelpers.IgnoreFirstAttribute.#ctor(System.Int32)">
            <summary>Indicates the number of lines at beginning of the file to be ignored.</summary>
            <param name="numberOfLines">The number of lines to be ignored.</param>
        </member>
        <member name="T:FileHelpers.IgnoreInheritedClassAttribute">
            <summary>Fields inherited from base classes will be ignored.</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="M:FileHelpers.IgnoreInheritedClassAttribute.#ctor">
            <summary>Fields inherited from base classes will be ignored.</summary>
        </member>
        <member name="T:FileHelpers.IgnoreLastAttribute">
            <summary>
            The number of lines to be ignored at the end of the file.
            </summary>
            <remarks>
            This is useful to discard trailer records from an incoming file.
            <para/>
            See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a> for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.IgnoreLastAttribute.NumberOfLines">
            <summary> The number of lines to be ignored at end of the file. </summary>
        </member>
        <member name="M:FileHelpers.IgnoreLastAttribute.#ctor">
            <summary>Indicates that the last line of the file will be ignored.</summary>
        </member>
        <member name="M:FileHelpers.IgnoreLastAttribute.#ctor(System.Int32)">
            <summary>Indicates the number of lines at end of the file that will be ignored.</summary>
            <param name="numberOfLines">The number of lines to be ignored at end of the file.</param>
        </member>
        <member name="T:FileHelpers.TypedRecordAttribute">
            <summary>Base class for the record types..</summary>
            <remarks>See the <a href="http://www.filehelpers.net/mustread">complete attributes list</a>
            for more information and examples of each one.</remarks>
        </member>
        <member name="P:FileHelpers.TypedRecordAttribute.DefaultCultureName">
            <summary>
            Default culture name used for each properties if no converter is specified otherwise.
            If null, the default decimal separator (".") will be used.
            </summary>
        </member>
        <member name="M:FileHelpers.TypedRecordAttribute.#ctor(System.String)">
            <summary>Abstract class, see inheritors.</summary>
        </member>
        <member name="T:FileHelpers.RecordIndexer">
            <summary>
            A class to loop through the field values
            </summary>
        </member>
        <member name="M:FileHelpers.RecordIndexer.#ctor">
            <summary>
            Get the record indexer,  engine will load the lines into an array
            </summary>
        </member>
        <member name="P:FileHelpers.RecordIndexer.FieldCount">
            <summary>
            The number of fields in the record
            </summary>
        </member>
        <member name="P:FileHelpers.RecordIndexer.Item(System.Int32)">
            <summary>
            Get the field value at the specified index.
            </summary>
            <param name="index">The index of the field (zero based)</param>
            <returns>The field value</returns>
        </member>
        <member name="M:FileHelpers.RecordIndexer.System#Collections#Generic#IEnumerable{System#String}#GetEnumerator">
            <summary>
            Get the enumerator of the list
            </summary>
            <returns></returns>
        </member>
        <member name="M:FileHelpers.RecordIndexer.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Get enumerator of the list
            </summary>
            <returns></returns>
        </member>
        <member name="T:FileHelpers.RecordIndexer.ArrayEnumerator">
            <summary>
            Create an enumerator off an array of strings
            </summary>
        </member>
        <member name="F:FileHelpers.RecordIndexer.ArrayEnumerator.mValues">
            <summary>
            Array to return one at a time
            </summary>
        </member>
        <member name="F:FileHelpers.RecordIndexer.ArrayEnumerator.i">
            <summary>
            Position in enumerator,  -1 to start
            </summary>
        </member>
        <member name="M:FileHelpers.RecordIndexer.ArrayEnumerator.#ctor(System.String[])">
            <summary>
            Create an enumerator off a string array
            </summary>
            <param name="values">values to return one at a time</param>
        </member>
        <member name="P:FileHelpers.RecordIndexer.ArrayEnumerator.System#Collections#Generic#IEnumerator{System#String}#Current">
            <summary>
            Get the current item we are working on
            </summary>
        </member>
        <member name="M:FileHelpers.RecordIndexer.ArrayEnumerator.Dispose">
            <summary>
            Clean up not needed
            </summary>
        </member>
        <member name="M:FileHelpers.RecordIndexer.ArrayEnumerator.MoveNext">
            <summary>
            move the pointer along
            </summary>
            <returns></returns>
        </member>
        <member name="M:FileHelpers.RecordIndexer.ArrayEnumerator.Reset">
            <summary>
            Go back to the start
            </summary>
        </member>
        <member name="P:FileHelpers.RecordIndexer.ArrayEnumerator.Current">
            <summary>
            Get the current element
            </summary>
        </member>
        <member name="T:FileHelpers.Converters.BooleanConverter">
            <summary>
            Convert an input value to a boolean,  allows for true false values
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.BooleanConverter.#ctor">
            <summary>
            Simple boolean converter
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.BooleanConverter.#ctor(System.String,System.String)">
            <summary>
            Boolean converter with true false values
            </summary>
            <param name="trueStr">True string</param>
            <param name="falseStr">False string</param>
        </member>
        <member name="M:FileHelpers.Converters.BooleanConverter.StringToField(System.String)">
            <summary>
            convert a string to a boolean value
            </summary>
            <param name="from">string to convert</param>
            <returns>boolean value</returns>
        </member>
        <member name="M:FileHelpers.Converters.BooleanConverter.FieldToString(System.Object)">
            <summary>
            Convert to a true false string
            </summary>
        </member>
        <member name="T:FileHelpers.Converters.ByteConverter">
            <summary>
            Convert a string into a byte value
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.ByteConverter.#ctor">
            <summary>
            Convert a string to a byte value using the default decimal separator
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.ByteConverter.#ctor(System.String)">
            <summary>
            Convert a string to a byte
            </summary>
            <param name="decimalSepOrCultureName">decimal separator to use '.' or ','</param>
        </member>
        <member name="M:FileHelpers.Converters.ByteConverter.StringToField(System.String)">
            <summary>
            Convert a string to a byte value
            </summary>
            <param name="from">string to parse</param>
            <returns>byte value</returns>
        </member>
        <member name="T:FileHelpers.Converters.CharConverter">
            <summary>
            Allow characters to be converted to upper and lower case automatically.
            </summary>
        </member>
        <member name="T:FileHelpers.Converters.CharConverter.CharFormat">
            <summary>
            whether we upper or lower case the character on input
            </summary>
        </member>
        <member name="F:FileHelpers.Converters.CharConverter.CharFormat.NoChange">
            <summary>
            Don't change the case
            </summary>
        </member>
        <member name="F:FileHelpers.Converters.CharConverter.CharFormat.Lower">
            <summary>
            Change to lower case
            </summary>
        </member>
        <member name="F:FileHelpers.Converters.CharConverter.CharFormat.Upper">
            <summary>
            change to upper case
            </summary>
        </member>
        <member name="F:FileHelpers.Converters.CharConverter.mFormat">
            <summary>
            default to not upper or lower case
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.CharConverter.#ctor">
            <summary>
            Create a single character converter that does not upper or lower case result
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.CharConverter.#ctor(System.String)">
            <summary>
            Single character converter that optionally makes it upper (X) or lower case (x)
            </summary>
            <param name="format"> empty string for no upper or lower,  x for lower case,  X for Upper case</param>
        </member>
        <member name="M:FileHelpers.Converters.CharConverter.StringToField(System.String)">
            <summary>
            Extract the first character with optional upper or lower case
            </summary>
            <param name="from">String contents</param>
            <returns>Character (may be upper or lower case)</returns>
        </member>
        <member name="M:FileHelpers.Converters.CharConverter.FieldToString(System.Object)">
            <summary>
            Convert from a character to a string for output
            </summary>
            <param name="from">Character to convert from</param>
            <returns>String containing the character</returns>
        </member>
        <member name="T:FileHelpers.Converters.ConvertHelpers">
            <summary>
            Class that provides static methods that returns a default 
            <see cref="T:FileHelpers.ConverterBase">Converter</see> to the basic types.
            </summary>
            <remarks>
                Used by the <see cref="T:FileHelpers.FieldConverterAttribute"/>.
            </remarks>
        </member>
        <member name="M:FileHelpers.Converters.ConvertHelpers.GetDefaultConverter(System.String,System.Type,System.String)">
            <summary>
            Check the type of the field and then return a converter for that particular type
            </summary>
            <param name="fieldName">Field name to check</param>
            <param name="fieldType">Type of the field to check</param>
            <param name="defaultCultureName">Default culture name used for each properties if no converter is specified otherwise. If null, the default decimal separator (".") will be used.</param>
            <returns>Converter for this particular field</returns>
        </member>
        <member name="M:FileHelpers.Converters.ConvertHelpers.RemoveBlanks(System.String)">
            <summary>
            Remove leading blanks and blanks after the plus or minus sign from a string
            to allow it to be parsed by ToInt or other converters
            </summary>
            <param name="source">source to trim</param>
            <returns>String without blanks</returns>
            <remarks>
            This logic is used to handle strings line " +  21 " from
            input data (returns "+21 "). The integer convert would fail
            because of the extra blanks so this logic trims them
            </remarks>
        </member>
        <member name="T:FileHelpers.Converters.CultureConverter">
            <summary>
            Convert a numeric value with separators into a value
            </summary>
        </member>
        <member name="F:FileHelpers.Converters.CultureConverter.mCulture">
            <summary>
            Culture information based on the separator
            </summary>
        </member>
        <member name="F:FileHelpers.Converters.CultureConverter.mType">
            <summary>
            Type for field being converted
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.CultureConverter.#ctor(System.Type,System.String)">
            <summary>
            Convert to a type given a decimal separator
            </summary>
            <param name="T">type we are converting</param>
            <param name="decimalSepOrCultureName">Separator or culture name (eg. 'en-US', 'fr-FR'...)</param>
        </member>
        <member name="M:FileHelpers.Converters.CultureConverter.FieldToString(System.Object)">
            <summary>
            Convert the field to a string representation
            </summary>
            <param name="from">Object to convert</param>
            <returns>string representation</returns>
        </member>
        <member name="M:FileHelpers.Converters.CultureConverter.CreateCulture(System.String)">
            <summary>
            Return culture information for with comma decimal separator or comma decimal separator
            </summary>
            <param name="decimalSepOrCultureName">Decimal separator string or culture name</param>
            <returns>Cultural information based on separator</returns>
        </member>
        <member name="T:FileHelpers.Converters.DateTimeConverter">
            <summary>
            Convert a value to a date time value
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeConverter.#ctor">
            <summary>
            Convert a value to a date time value
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeConverter.#ctor(System.String)">
            <summary>
            Convert a value to a date time value
            </summary>
            <param name="format">date format see .Net documentation</param>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeConverter.#ctor(System.String,System.String)">
            <summary>
            Convert a value to a date time value
            </summary>
            <param name="format">date format see .Net documentation</param>
            <param name="culture">The culture used to parse the Dates</param>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeConverter.StringToField(System.String)">
            <summary>
            Convert a string to a date time value
            </summary>
            <param name="from">String value to convert</param>
            <returns>DateTime value</returns>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeConverter.FieldToString(System.Object)">
            <summary>
            Convert a date time value to a string
            </summary>
            <param name="from">DateTime value to convert</param>
            <returns>string DateTime value</returns>
        </member>
        <member name="T:FileHelpers.Converters.DateTimeMultiFormatConverter">
            <summary>
            Convert a value to a date time value
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeMultiFormatConverter.#ctor(System.String,System.String)">
            <summary>
            Convert a value to a date time value using multiple formats
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeMultiFormatConverter.#ctor(System.String,System.String,System.String)">
            <summary>
            Convert a value to a date time value using multiple formats
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeMultiFormatConverter.#ctor(System.String[])">
            <summary>
            Convert a date time value to a string
            </summary>
            <param name="formats">list of formats to try</param>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeMultiFormatConverter.StringToField(System.String)">
            <summary>
            Convert a date time value to a string
            </summary>
            <param name="from">DateTime value to convert</param>
            <returns>string DateTime value</returns>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeMultiFormatConverter.CreateFormats">
            <summary>
            Create a list of formats to pass to the DateTime tryparse function
            </summary>
            <returns>string DateTime value</returns>
        </member>
        <member name="M:FileHelpers.Converters.DateTimeMultiFormatConverter.FieldToString(System.Object)">
            <summary>
            Convert a date time value to a string (uses first format for output
            </summary>
            <param name="from">DateTime value to convert</param>
            <returns>string DateTime value</returns>
        </member>
        <member name="T:FileHelpers.Converters.DecimalConverter">
            <summary>
            Convert a value to a decimal value
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.DecimalConverter.#ctor">
            <summary>
            Convert a value to a decimal value
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.DecimalConverter.#ctor(System.String)">
            <summary>
            Convert a value to a decimal value
            </summary>
            <param name="decimalSepOrCultureName">dot or comma for separator</param>
        </member>
        <member name="M:FileHelpers.Converters.DecimalConverter.StringToField(System.String)">
            <summary>
            Convert a string to a decimal
            </summary>
            <param name="from">String value to convert</param>
            <returns>decimal value</returns>
        </member>
        <member name="T:FileHelpers.Converters.DoubleConverter">
            <summary>
            Convert a value to a single floating point
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.DoubleConverter.#ctor">
            <summary>
            Convert a value to a floating point
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.DoubleConverter.#ctor(System.String)">
            <summary>
            Convert a value to a floating point
            </summary>
            <param name="decimalSepOrCultureName">dot or comma for separator</param>
        </member>
        <member name="M:FileHelpers.Converters.DoubleConverter.StringToField(System.String)">
            <summary>
            Convert a string to an floating point
            </summary>
            <param name="from">String value to convert</param>
            <returns>Floating point value</returns>
        </member>
        <member name="T:FileHelpers.Converters.GuidConverter">
            <summary>
             Convert a GUID to and from a field value
            </summary>
        </member>
        <member name="F:FileHelpers.Converters.GuidConverter.mFormat">
            <summary>
            D or N or B or P (default is D: see Guid.ToString(string format))
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.GuidConverter.#ctor">
            <summary>
            Create a GUID converter with the default format code "D"
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.GuidConverter.#ctor(System.String)">
            <summary>
            Create a GUID converter with formats as defined for GUID
            N, D, B or P
            </summary>
            <param name="format">Format code for GUID</param>
        </member>
        <member name="M:FileHelpers.Converters.GuidConverter.StringToField(System.String)">
            <summary>
            Convert a GUID string to a GUID object for the record object
            </summary>
            <param name="from">String representation of the GUID</param>
            <returns>GUID object or GUID empty</returns>
        </member>
        <member name="M:FileHelpers.Converters.GuidConverter.FieldToString(System.Object)">
            <summary>
            Output GUID as a string field
            </summary>
            <param name="from">Guid object</param>
            <returns>GUID as a string depending on format</returns>
        </member>
        <member name="T:FileHelpers.Converters.IConverter">
            <summary>
            Can be used both from Attributes as with the fluent configuration.
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.IConverter.StringToField(System.String)">
            <summary>
            Convert a string in the file to a field value.
            </summary>
            <param name="from">The string to convert.</param>
            <returns>The Field value.</returns>
        </member>
        <member name="M:FileHelpers.Converters.IConverter.FieldToString(System.Object)">
            <summary>
            Convert a field value to an string to write this to the file.
            </summary>
            <remarks>The basic implementation just returns  from.ToString();</remarks>
            <param name="from">The field values to convert, can be null</param>
            <returns>The string representing the field value, must return a string, can be string.empty</returns>
        </member>
        <member name="T:FileHelpers.Converters.Int16Converter">
            <summary>
            Convert a value to a short integer
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.Int16Converter.#ctor">
            <summary>
            Convert a value to a short integer
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.Int16Converter.#ctor(System.String)">
            <summary>
            Convert a value to a short integer
            </summary>
            <param name="decimalSepOrCultureName">dot or comma for separator</param>
        </member>
        <member name="M:FileHelpers.Converters.Int16Converter.StringToField(System.String)">
            <summary>
            Convert a string to an short integer
            </summary>
            <param name="from">String value to convert</param>
            <returns>Short signed value</returns>
        </member>
        <member name="T:FileHelpers.Converters.Int32Converter">
            <summary>
            Convert a value to a integer
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.Int32Converter.#ctor">
            <summary>
            Convert a value to a integer
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.Int32Converter.#ctor(System.String)">
            <summary>
            Convert a value to a integer
            </summary>
            <param name="decimalSepOrCultureName">dot or comma for separator</param>
        </member>
        <member name="M:FileHelpers.Converters.Int32Converter.StringToField(System.String)">
            <summary>
            Convert a string to an integer
            </summary>
            <param name="from">String value to convert</param>
            <returns>integer value</returns>
        </member>
        <member name="T:FileHelpers.Converters.Int64Converter">
            <summary>
            Convert a value to a long integer
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.Int64Converter.#ctor">
            <summary>
            Convert a value to a long integer
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.Int64Converter.#ctor(System.String)">
            <summary>
            Convert a value to a long integer
            </summary>
            <param name="decimalSepOrCultureName">dot or comma for separator</param>
        </member>
        <member name="M:FileHelpers.Converters.Int64Converter.StringToField(System.String)">
            <summary>
            Convert a string to an integer long
            </summary>
            <param name="from">String value to convert</param>
            <returns>Long value</returns>
        </member>
        <member name="T:FileHelpers.Converters.PercentDoubleConverter">
            <summary>
            This Class is specialized version of the Double Converter
            The main difference being that it can handle % sign at the end of the number
            It gives a value which is basically number / 100.
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.PercentDoubleConverter.#ctor">
            <summary>
            Convert a value to a floating point from a percentage
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.PercentDoubleConverter.#ctor(System.String)">
            <summary>
            Convert a value to a floating point from a percentage
            </summary>
            <param name="decimalSepOrCultureName">dot or comma for separator</param>
        </member>
        <member name="M:FileHelpers.Converters.PercentDoubleConverter.StringToField(System.String)">
            <summary>
            Convert a string to an floating point from percentage
            </summary>
            <param name="from">String value to convert</param>
            <returns>floating point value</returns>
        </member>
        <member name="T:FileHelpers.Converters.SByteConverter">
            <summary>
            Signed byte converter (8 bit signed integer)
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.SByteConverter.#ctor">
            <summary>
            Signed byte converter (8 bit signed integer)
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.SByteConverter.#ctor(System.String)">
            <summary>
            Signed byte converter (8 bit signed integer)
            </summary>
            <param name="decimalSepOrCultureName">dot or comma for separator</param>
        </member>
        <member name="M:FileHelpers.Converters.SByteConverter.StringToField(System.String)">
            <summary>
            Convert a string to an signed byte
            </summary>
            <param name="from">String value to convert</param>
            <returns>Signed byte value</returns>
        </member>
        <member name="T:FileHelpers.Converters.SingleConverter">
            <summary>
            Convert a value to a single floating point
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.SingleConverter.#ctor">
            <summary>
            Convert a value to a single floating point
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.SingleConverter.#ctor(System.String)">
            <summary>
            Convert a value to a single floating point
            </summary>
            <param name="decimalSepOrCultureName">dot or comma for separator</param>
        </member>
        <member name="M:FileHelpers.Converters.SingleConverter.StringToField(System.String)">
            <summary>
            Convert a string to an single precision floating point
            </summary>
            <param name="from">String value to convert</param>
            <returns>Single floating point value</returns>
        </member>
        <member name="T:FileHelpers.Converters.UInt16Converter">
            <summary>
            Convert a string to a short integer
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.UInt16Converter.#ctor">
            <summary>
            Convert a number to a short integer
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.UInt16Converter.#ctor(System.String)">
            <summary>
            Convert a number to a short integer
            </summary>
            <param name="decimalSepOrCultureName">Decimal separator</param>
        </member>
        <member name="M:FileHelpers.Converters.UInt16Converter.StringToField(System.String)">
            <summary>
            Parse a string to a short integer
            </summary>
            <param name="from">string representing short integer</param>
            <returns>short integer value</returns>
        </member>
        <member name="T:FileHelpers.Converters.UInt32Converter">
            <summary>
            Unsigned integer converter
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.UInt32Converter.#ctor">
            <summary>
            Unsigned integer converter
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.UInt32Converter.#ctor(System.String)">
            <summary>
            Unsigned integer converter with a decimal separator
            </summary>
            <param name="decimalSepOrCultureName">dot or comma for to separate decimal</param>
        </member>
        <member name="M:FileHelpers.Converters.UInt32Converter.StringToField(System.String)">
            <summary>
            Convert a string to a unsigned integer value
            </summary>
            <param name="from">String value to parse</param>
            <returns>Unsigned integer object</returns>
        </member>
        <member name="T:FileHelpers.Converters.UInt64Converter">
            <summary>
            Unsigned long converter
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.UInt64Converter.#ctor">
            <summary>
            Unsigned long converter
            </summary>
        </member>
        <member name="M:FileHelpers.Converters.UInt64Converter.#ctor(System.String)">
            <summary>
            Unsigned long with decimal separator
            </summary>
            <param name="decimalSepOrCultureName">dot or comma for separator</param>
        </member>
        <member name="M:FileHelpers.Converters.UInt64Converter.StringToField(System.String)">
            <summary>
            Convert a string to an unsigned integer long
            </summary>
            <param name="from">String value to convert</param>
            <returns>Unsigned long value</returns>
        </member>
        <member name="T:FileHelpers.ConverterBase">
            <summary>
            Base class to provide bi-directional
            Field - String conversion.
            </summary>
        </member>
        <member name="P:FileHelpers.ConverterBase.DefaultDateTimeFormat">
            <summary>
            <para>Allow you to set the default Date Format used for the Converter.</para>
            <para>using the same CustomDateTimeFormat that is used in the .NET framework.</para>
            <para>By default: "ddMMyyyy"</para>
            </summary>
        </member>
        <member name="M:FileHelpers.ConverterBase.StringToField(System.String)">
            <summary>
            Convert a string in the file to a field value.
            </summary>
            <param name="from">The string to convert.</param>
            <returns>The Field value.</returns>
        </member>
        <member name="M:FileHelpers.ConverterBase.FieldToString(System.Object)">
            <summary>
            Convert a field value to an string to write this to the file.
            </summary>
            <remarks>The basic implementation just returns  from.ToString();</remarks>
            <param name="from">The field values to convert.</param>
            <returns>The string representing the field value.</returns>
        </member>
        <member name="P:FileHelpers.ConverterBase.CustomNullHandling">
            <summary>
            If the class returns false the engines don't pass null values to the converter. 
            If true the engines pass all the values to the converter.
            </summary>
        </member>
        <member name="M:FileHelpers.ConverterBase.ThrowConvertException(System.String,System.String)">
            <summary>
            Throws a ConvertException with the passed values
            </summary>
            <param name="from">The source string.</param>
            <param name="errorMsg" >The custom error msg.</param>
            <exception cref="T:FileHelpers.ConvertException">Throw exception with values</exception>
        </member>
        <member name="T:FileHelpers.EnumConverter.EnumFormat">
            <summary>
            whether to output enum as string or as integer
            </summary>
        </member>
        <member name="F:FileHelpers.EnumConverter.EnumFormat.String">
            <summary>
            as string
            </summary>
        </member>
        <member name="F:FileHelpers.EnumConverter.EnumFormat.Number">
            <summary>
            as integer
            </summary>
        </member>
        <member name="M:FileHelpers.Core.Attributes.GetFirst``1(System.Reflection.MemberInfo)">
            <summary>
            Get the first attribute that matches the type specification (not inherited)
            </summary>
            <typeparam name="T">Type of attribute to find</typeparam>
            <param name="type">Member info to look at for attributes</param>
            <returns>attribute or null</returns>
        </member>
        <member name="M:FileHelpers.Core.Attributes.GetFirstInherited``1(System.Reflection.MemberInfo)">
            <summary>
            Get the first attribute of this type (or its children)
            </summary>
            <typeparam name="T">Type of attribute</typeparam>
            <param name="type">member info containing attributes</param>
            <returns>Found attribute or null</returns>
        </member>
        <member name="M:FileHelpers.Core.Attributes.GetFirstCore``1(System.Reflection.MemberInfo,System.Boolean)">
            <summary>
            Worker to get attributes from MemberInfo details
            </summary>
            <typeparam name="T">Type to create</typeparam>
            <param name="type">Member Info details</param>
            <param name="inherited">Allow inherited version of attribute?</param>
            <returns>Attribute found or null</returns>
        </member>
        <member name="M:FileHelpers.Core.Attributes.WorkWithFirst``1(System.Reflection.MemberInfo,System.Action{``0})">
            <summary>
            Locate an attribute and perform an Action on it, do nothing if you don't find it
            </summary>
            <typeparam name="T">Type of attribute we are looking for</typeparam>
            <param name="type">Field type we are analysing</param>
            <param name="action">action we want to perform</param>
        </member>
        <member name="M:FileHelpers.Core.ComparerCache.CreateComparer">
            <summary>
            Create an invariant culture comparison operator
            </summary>
            <returns>Comparison operations</returns>
        </member>
        <member name="T:FileHelpers.Core.ConditionHelper">
            <summary>
            Helpers that work with conditions to make them easier to write
            </summary>
        </member>
        <member name="M:FileHelpers.Core.ConditionHelper.BeginsWith(System.String,System.String)">
            <summary>
            Test whether string begins with another string
            </summary>
            <param name="line">string to test</param>
            <param name="selector">value we want to check for</param>
            <returns>true if string begins with the selector</returns>
        </member>
        <member name="M:FileHelpers.Core.ConditionHelper.EndsWith(System.String,System.String)">
            <summary>
            Test whether string ends with another string
            </summary>
            <param name="line">string to test</param>
            <param name="selector">value we want to check for</param>
            <returns>true if string ends with the selector</returns>
        </member>
        <member name="M:FileHelpers.Core.ConditionHelper.Contains(System.String,System.String)">
            <summary>
            Test whether string contains with another string
            </summary>
            <param name="line">string to test</param>
            <param name="selector">value we want to check for</param>
            <returns>true if string contains the selector</returns>
        </member>
        <member name="M:FileHelpers.Core.ConditionHelper.Enclosed(System.String,System.String)">
            <summary>
            Test whether string begins and ends with another string
            </summary>
            <param name="line">string to test</param>
            <param name="selector">value we want to check for</param>
            <returns>true if string begins and ends with the selector</returns>
        </member>
        <member name="T:FileHelpers.ExtensionsFileHelpers">
            <summary>
            Set of Extension methods to be exposed to end users 
            of the FileHelpers API.
            </summary>
        </member>
        <member name="M:FileHelpers.ExtensionsFileHelpers.ToDataTable``1(``0[])">
            <summary>
            Generic extension method for arrays that returns the array records
            in a DataTable.
            </summary>
            <param name="records">The array to transform into a DataTable</param>
            <returns>The array records in a DataTable.</returns>
        </member>
        <member name="T:FileHelpers.ExtractedInfo">
            <summary>
            A single field extracted from the 'record'
            </summary>
            <remarks>
            Record is defined by the way the data is input
            </remarks>
        </member>
        <member name="F:FileHelpers.ExtractedInfo.mCustomExtractedString">
            <summary>
            Allows for the actual string to be overridden 
            </summary>
        </member>
        <member name="M:FileHelpers.ExtractedInfo.ExtractedString">
            <summary>
            The string value of the field extracted from the record
            </summary>
            <returns></returns>
        </member>
        <member name="F:FileHelpers.ExtractedInfo.mLine">
            <summary>
            Contains the line of data read
            </summary>
        </member>
        <member name="F:FileHelpers.ExtractedInfo.ExtractedFrom">
            <summary>
            Position of first character of the field in mLine.mLine
            </summary>
        </member>
        <member name="F:FileHelpers.ExtractedInfo.ExtractedTo">
            <summary>
            Position of last character of the field in mLine.mLine
            </summary>
        </member>
        <member name="M:FileHelpers.ExtractedInfo.#ctor(FileHelpers.LineInfo)">
            <summary>
            Extract the rest of the line into my variable
            </summary>
            <param name="line"></param>
        </member>
        <member name="M:FileHelpers.ExtractedInfo.#ctor(FileHelpers.LineInfo,System.Int32)">
            <summary>
            Extract field from current position to specified position
            </summary>
            <param name="line">Record information</param>
            <param name="extractTo">Position to extract to</param>
        </member>
        <member name="M:FileHelpers.ExtractedInfo.#ctor(System.String)">
            <summary>
            Allow a default string or a specific string for this
            variable to be applied
            </summary>
            <param name="customExtract"></param>
        </member>
        <member name="M:FileHelpers.FieldInfoCacheManipulator.ResetFieldInfoCache(System.Type)">
            <summary>
            Very importat to avoid out of order reflection
            The CLR caches previous fields access to speed up reflection but can return the fields in wrong order
            Clearing the m_fieldInfoCache of the Cache property resolves the issue
            </summary>
            <param name="type">Type of Object</param>
        </member>
        <member name="F:FileHelpers.ForwardReader.mReader">
            <summary>
            Return file record by record
            </summary>
        </member>
        <member name="F:FileHelpers.ForwardReader.mFowardStrings">
            <summary>
            records already read
            </summary>
        </member>
        <member name="M:FileHelpers.ForwardReader.#ctor(FileHelpers.IRecordReader,System.Int32)">
            <summary>
            Read a Record handler forward,  optionally skipping n lines and starting at a record number > 0
            </summary>
            <param name="reader">Reader to get records</param>
            <param name="forwardLines">Number of lines to skip before reading</param>
        </member>
        <member name="M:FileHelpers.ForwardReader.#ctor(FileHelpers.IRecordReader,System.Int32,System.Int32)">
            <summary>
            Read a Record handler forward,  optionally skipping n lines and starting at a record number > 0
            </summary>
            <param name="reader">Reader to get records</param>
            <param name="forwardLines">Number of lines to skip before reading</param>
            <param name="startLine">Lines already read from file</param>
        </member>
        <member name="P:FileHelpers.ForwardReader.LineNumber">
            <summary>
            Record number within the file - normally the line number
            </summary>
        </member>
        <member name="M:FileHelpers.ForwardReader.Close">
            <summary>
            Close the record reader, which should in turn close the stream
            </summary>
        </member>
        <member name="T:FileHelpers.IRecordInfo">
            <summary>
            Record information,  whether it is delimited or other details
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.IsDelimited">
            <summary>
            Is the input delimited or fixed length
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.FieldCount">
            <summary>
            Number of fields defined
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.Fields">
            <summary>
            List of fields in order read from record
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.IgnoreFirst">
            <summary>
            Number of records to skip before starting processing (header records)
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.IgnoreLast">
            <summary>
            Number of records to skip when processing end of file (Trailer records)
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.NotifyRead">
            <summary>
            Whether the Notify read event is set or something else needs to be notified of change
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.NotifyWrite">
            <summary>
            Whether the notify write event is hooked or something else needs to be notified on write
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.SizeHint">
            <summary>
            Buffer beginning size hint
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.RecordType">
            <summary>
            Type of object to be created
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.IgnoreEmptyLines">
            <summary>
            Do we skip empty lines?
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.IgnoreEmptySpaces">
            <summary>
            Do we skip lines that are visually empty,  spaces only
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.CommentMarker">
            <summary>
            String that prefixes a comment,  eg a # in shell script
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.CommentAnyPlace">
            <summary>
            Can the comment marker be proceeded with spaces.
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.RecordCondition">
            <summary>
            Selection condition when reading records, allows skipping unused data
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.RecordConditionRegEx">
            <summary>
            Selection of records to process by regex
            </summary>
        </member>
        <member name="P:FileHelpers.IRecordInfo.RecordConditionSelector">
            <summary>
            Selection of records to process by prefix
            </summary>
        </member>
        <member name="M:FileHelpers.IRecordInfo.GetFieldIndex(System.String)">
            <summary>
            Get the position in the list of FieldName
            </summary>
            <param name="fieldName">name to look up</param>
            <returns>Position in list</returns>
        </member>
        <member name="M:FileHelpers.IRecordInfo.GetFieldInfo(System.String)">
            <summary>
            /Get the complete information about this field
            </summary>
            <param name="name">FieldName to look up for information</param>
            <returns>FieldInfo on the name</returns>
        </member>
        <member name="P:FileHelpers.IRecordInfo.Operations">
            <summary>
            Cache of routines to handle various operations on the record
            </summary>
        </member>
        <member name="T:FileHelpers.IRecordReader">
            <summary>
            Basic read record interface
            </summary>
        </member>
        <member name="M:FileHelpers.IRecordReader.ReadRecordString">
            <summary>
            Read a record from the data source
            </summary>
            <returns>A single record for parsing</returns>
        </member>
        <member name="M:FileHelpers.IRecordReader.Close">
            <summary>
            close the interface and return
            </summary>
        </member>
        <member name="T:FileHelpers.LineInfo">
            <summary>
            Record read from the file for processing
            </summary>
            <remarks>
            The data inside the LIneInfo may be reset during processing,
            for example on read next line.  Do not rely on this class
            containing all data from a record for all time.  It is designed
            to read data sequentially.
            </remarks>
        </member>
        <member name="M:FileHelpers.LineInfo.#ctor(System.String)">
            <summary>
            Create a line info with data from line
            </summary>
            <param name="line"></param>
        </member>
        <member name="M:FileHelpers.LineInfo.Substring(System.Int32,System.Int32)">
            <summary>
            Return part of line,  Substring
            </summary>
            <param name="from">Start position (zero offset)</param>
            <param name="count">Number of characters to extract</param>
            <returns>substring from line</returns>
        </member>
        <member name="F:FileHelpers.LineInfo.mLineStr">
            <summary>
            Record read from reader
            </summary>
        </member>
        <member name="F:FileHelpers.LineInfo.mReader">
            <summary>
            Reader that got the string
            </summary>
        </member>
        <member name="F:FileHelpers.LineInfo.mCurrentPos">
            <summary>
            Where we are processing records from
            </summary>
        </member>
        <member name="F:FileHelpers.LineInfo.WhitespaceChars">
            <summary>
            List of whitespace characters that we want to skip
            </summary>
        </member>
        <member name="M:FileHelpers.LineInfo.DebuggerDisplayStr">
            <summary>
            Debugger display string
            </summary>
            <returns></returns>
        </member>
        <member name="P:FileHelpers.LineInfo.CurrentString">
            <summary>
            Extract a single field from the system
            </summary>
        </member>
        <member name="M:FileHelpers.LineInfo.IsEOL">
            <summary>
            If we have extracted more that the field contains.
            </summary>
            <returns>True if end of line</returns>
        </member>
        <member name="P:FileHelpers.LineInfo.CurrentLength">
            <summary>
            Amount of data left to process
            </summary>
        </member>
        <member name="M:FileHelpers.LineInfo.EmptyFromPos">
            <summary>
            Is there only whitespace left in the record?
            </summary>
            <returns>True if only whitespace</returns>
        </member>
        <member name="M:FileHelpers.LineInfo.TrimStart(System.Char[])">
            <summary>
            Delete any of these characters from beginning of the record
            </summary>
            <param name="toTrim"></param>
        </member>
        <member name="M:FileHelpers.LineInfo.TrimStartSorted(System.Char[])">
            <summary>
            Move the record pointer along skipping these characters
            </summary>
            <param name="toTrim">Sorted array of character to skip</param>
        </member>
        <member name="M:FileHelpers.LineInfo.StartsWithTrim(System.String)">
            <summary>
            Check that the record begins with a value ignoring whitespace
            </summary>
            <param name="str">String to check for</param>
            <returns>True if record begins with</returns>
        </member>
        <member name="M:FileHelpers.LineInfo.ReadNextLine">
            <summary>
            get The next line from the system and reset the line pointer to zero
            </summary>
        </member>
        <member name="M:FileHelpers.LineInfo.IndexOf(System.String)">
            <summary>
            Find the location of the next string in record
            </summary>
            <param name="foundThis">String we are looking for</param>
            <returns>Position of the next one</returns>
        </member>
        <member name="M:FileHelpers.LineInfo.ReLoad(System.String)">
            <summary>
            Reset the string back to the original line and reset the line pointer
            </summary>
            <remarks>If the input is multi line, this will read next record and remove the original data</remarks>
            <param name="line">Line to use</param>
        </member>
        <member name="T:FileHelpers.NewLineDelimitedRecordReader">
            <summary>
            Read a record that is delimited by a newline
            </summary>
        </member>
        <member name="M:FileHelpers.NewLineDelimitedRecordReader.#ctor(System.IO.TextReader)">
            <summary>
            Read a file line by line from the specified textreader
            </summary>
            <param name="reader">TextReader to read and process</param>
        </member>
        <member name="M:FileHelpers.NewLineDelimitedRecordReader.ReadRecordString">
            <summary>
            Read a record from the file
            </summary>
            <returns>unprocessed record</returns>
        </member>
        <member name="M:FileHelpers.NewLineDelimitedRecordReader.Close">
            <summary>
            Close the reader
            </summary>
        </member>
        <member name="T:FileHelpers.RecordCondition">
            <summary>The condition used to include or exclude each record.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.None">
            <summary>No Condition, Include it always.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.IncludeIfContains">
            <summary>Include the record if it contains the selector string.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.IncludeIfBegins">
            <summary>Include the record if it begins with selector string.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.IncludeIfEnds">
            <summary>Include the record if it ends with selector string.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.IncludeIfEnclosed">
            <summary>Include the record if it begins and ends with selector string.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.IncludeIfMatchRegex">
            <summary>Include the record if it matches the regular expression passed as selector.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.ExcludeIfContains">
            <summary>Exclude the record if it contains the selector string.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.ExcludeIfBegins">
            <summary>Exclude the record if it begins with selector string.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.ExcludeIfEnds">
            <summary>Exclude the record if it ends with selector string.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.ExcludeIfEnclosed">
            <summary>Exclude the record if it begins and ends with selector string.</summary>
        </member>
        <member name="F:FileHelpers.RecordCondition.ExcludeIfMatchRegex">
            <summary>Exclude the record if it matches the regular expression passed as selector.</summary>
        </member>
        <member name="T:FileHelpers.RecordInfo">
            <summary>An internal class used to store information about the Record Type.</summary>
            <summary>An internal class used to store information about the Record Type.</summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.SizeHint">
            <summary>
            Hint about record size
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.RecordType">
            <summary>
            Class we are defining
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.IgnoreEmptyLines">
            <summary>
            Do we skip empty lines?
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.IgnoreEmptySpaces">
            <summary>
            Do we skip lines with completely blank lines
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.CommentMarker">
            <summary>
            Comment prefix
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.FieldCount">
            <summary>
            Number of fields we are processing
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.Fields">
            <summary>
            List of fields and the extraction details
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.IgnoreFirst">
            <summary>
            Number of lines to skip at beginning of file
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.IgnoreLast">
            <summary>
            Number of lines to skip at end of file
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.NotifyRead">
            <summary>
            DO we need to issue a Notify read
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.NotifyWrite">
            <summary>
            Do we need to issue a Notify Write
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.CommentAnyPlace">
            <summary>
            Can the comment prefix have leading whitespace
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.RecordCondition">
            <summary>
            Include or skip a record based upon a defined RecordCondition interface
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.RecordConditionRegEx">
            <summary>
            Skip or include a record based upon a regular expression
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.RecordConditionSelector">
            <summary>
            Include or exclude a record based upon presence of a string
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.Operations">
            <summary>
            Operations are the functions that perform creation or extraction of data from objects
            these are created dynamically from the record conditions
            </summary>
        </member>
        <member name="P:FileHelpers.RecordInfo.IsDelimited">
            <summary>
            Is this record layout delimited
            </summary>
        </member>
        <member name="M:FileHelpers.RecordInfo.#ctor(System.Type)">
            <summary>
            Read the attributes of the class and create an array
            of how to process the file
            </summary>
            <param name="recordType">Class we are analysing</param>
        </member>
        <member name="M:FileHelpers.RecordInfo.InitRecordFields">
            <summary>
            Create a list of fields we are extracting and set
            the size hint for record I/O
            </summary>
        </member>
        <member name="M:FileHelpers.RecordInfo.CreateCoreFields(System.Collections.Generic.IList{System.Reflection.FieldInfo},FileHelpers.TypedRecordAttribute)">
            <summary>
            Parse the attributes on the class and create an ordered list of
            fields we are extracting from the record
            </summary>
            <param name="fields">Complete list of fields in class</param>
            <param name="recordAttribute">Type of record, fixed or delimited</param>
            <returns>List of fields we are extracting</returns>
        </member>
        <member name="M:FileHelpers.RecordInfo.CheckForOptionalAndArrayProblems(System.Collections.Generic.List{FileHelpers.FieldBase})">
            <summary>
            Check that once one field is optional all following fields are optional
            <para/>
            Check that arrays in the middle of a record are of fixed length
            </summary>
            <param name="resFields">List of fields to extract</param>
        </member>
        <member name="M:FileHelpers.RecordInfo.SortFieldsByOrder(System.Collections.Generic.List{FileHelpers.FieldBase})">
            <summary>
            Sort fields by the order if supplied
            </summary>
            <param name="resFields">List of fields to use</param>
        </member>
        <member name="M:FileHelpers.RecordInfo.CheckForOrderProblems(FileHelpers.FieldBase,System.Collections.Generic.List{FileHelpers.FieldBase})">
            <summary>
            Confirm all fields are either ordered or unordered
            </summary>
            <param name="currentField">Newest field</param>
            <param name="resFields">Other fields we have found</param>
        </member>
        <member name="M:FileHelpers.RecordInfo.GetFieldIndex(System.String)">
            <summary>
            Get the index number of the fieldname in the field list
            </summary>
            <param name="fieldName">Fieldname to search for</param>
            <returns>Index in field list</returns>
        </member>
        <member name="M:FileHelpers.RecordInfo.GetFieldInfo(System.String)">
            <summary>
            Get field information base on name
            </summary>
            <param name="name">name to find details for</param>
            <returns>Field information</returns>
        </member>
        <member name="M:FileHelpers.RecordInfo.Resolve(System.Type)">
            <summary>
            Return the record type information for the record
            </summary>
            <param name="type">Type of object to create</param>
            <returns>Record info for that type</returns>
        </member>
        <member name="M:FileHelpers.RecordInfo.Clone">
            <summary>
            Create an new instance of the record information
            </summary>
            <returns>Deep copy of the RecordInfo class</returns>
        </member>
        <member name="M:FileHelpers.RecordInfo.CheckInterface(System.Type,System.Type)">
            <summary>
            Check whether the type implements the INotifyRead or INotifyWrite interfaces
            </summary>
            <param name="type">Type to check interface</param>
            <param name="interfaceType">Interface generic type we are checking for eg INotifyRead&lt;&gt;</param>
            <returns>Whether we found interface</returns>
        </member>
        <member name="M:FileHelpers.RecordInfo.RecordInfoFactory.Resolve(System.Type)">
            <summary>
            Return the record information for the type
            </summary>
            <param name="type">Type we want settings for</param>
            <remarks>Threadsafe</remarks>
            <returns>Record Information (settings and functions)</returns>
        </member>
        <member name="T:FileHelpers.RecordOperations">
            <summary>
            Collection of operations that we perform on a type, cached for reuse
            </summary>
        </member>
        <member name="F:FileHelpers.RecordOperations.mRecordInfo">
            <summary>
            Record Info we use to parse the record and generate an object instance
            </summary>
        </member>
        <member name="M:FileHelpers.RecordOperations.#ctor(FileHelpers.IRecordInfo)">
            <summary>
            Create a set of operations for a particular type
            </summary>
            <param name="recordInfo">Record details we create objects off</param>
        </member>
        <member name="M:FileHelpers.RecordOperations.StringToRecord(FileHelpers.LineInfo,System.Object[])">
            <summary>
            Process a line and turn it into an object
            </summary>
            <param name="line">Line of data to process</param>
            <param name="values">Values to assign to object</param>
            <returns>Object created or null if record skipped</returns>
            <exception cref="T:FileHelpers.ConvertException">Could not convert data from input file</exception>
        </member>
        <member name="M:FileHelpers.RecordOperations.StringToRecord(System.Object,FileHelpers.LineInfo,System.Object[])">
            <summary>
            Extract fields from record and assign values to the object
            </summary>
            <param name="record">Object to assign to</param>
            <param name="line">Line of data</param>
            <param name="values">Array of values extracted</param>
            <returns>true if we processed the line and updated object</returns>
        </member>
        <member name="M:FileHelpers.RecordOperations.MustIgnoreLine(System.String)">
            <summary>
            If we skip empty lines or it is a comment or is is excluded by settings
            </summary>
            <param name="line">Input line we are testing</param>
            <returns>True if line is skipped</returns>
        </member>
        <member name="M:FileHelpers.RecordOperations.RecordToString(System.Object)">
            <summary>
            Create a string of the object based on a record information supplied
            </summary>
            <param name="record">Object to convert</param>
            <returns>String representing the object</returns>
        </member>
        <member name="M:FileHelpers.RecordOperations.RecordValuesToString(System.Object[])">
            <summary>
            Assign a series of values out to a string based on file info layout
            </summary>
            <param name="recordValues">Values to write in order</param>
            <returns>String representing values</returns>
        </member>
        <member name="M:FileHelpers.RecordOperations.ValuesToRecord(System.Object[])">
            <summary>Returns a record formed with the passed values.</summary>
            <param name="values">The source Values.</param>
            <returns>A record formed with the passed values.</returns>
        </member>
        <member name="M:FileHelpers.RecordOperations.RecordToValues(System.Object)">
            <summary>Get an object[] of the values in the fields of the instance.</summary>
            <param name="record">Instance of the type.</param>
            <returns>An object[] of the values in the fields.</returns>
        </member>
        <member name="M:FileHelpers.RecordOperations.RecordsToDataTable(System.Collections.ICollection)">
            <summary>
            Create a datatable based on a collection of records
            </summary>
            <param name="records">Collection of records to process</param>
            <returns>datatable representing all records in collection</returns>
        </member>
        <member name="M:FileHelpers.RecordOperations.RecordsToDataTable(System.Collections.ICollection,System.Int32)">
            <summary>
            Create a data table containing at most maxRecords (-1 is unlimitted)
            </summary>
            <param name="records">Records to add to datatable</param>
            <param name="maxRecords">Maximum number of records (-1 is all)</param>
            <returns>Datatable based on record</returns>
        </member>
        <member name="M:FileHelpers.RecordOperations.CreateEmptyDataTable">
            <summary>
            Create an empty datatable based upon the record layout
            </summary>
            <returns>Datatable defined based on the record definition</returns>
        </member>
        <member name="P:FileHelpers.RecordOperations.ObjectToValuesHandler">
            <summary>
            Function to take and instance and return an array of objects
            Dynamically created when first used
            </summary>
        </member>
        <member name="P:FileHelpers.RecordOperations.CreateHandler">
            <summary>
            function to create the object and assign the values to that object
            </summary>
        </member>
        <member name="P:FileHelpers.RecordOperations.AssignHandler">
            <summary>
            First time through create a dynamic method to assign the data to the class object based on the fields
            </summary>
        </member>
        <member name="P:FileHelpers.RecordOperations.CreateRecordHandler">
            <summary>
            Create an instance of the object function
            </summary>
        </member>
        <member name="M:FileHelpers.RecordOperations.GetFieldInfoArray">
            <summary>
            Extract the fieldinfo from the record info
            </summary>
            <returns>array of fieldInfo</returns>
        </member>
        <member name="M:FileHelpers.RecordOperations.Clone(FileHelpers.RecordInfo)">
            <summary>
            Copy one object to another based on field list
            </summary>
            <param name="ri">Record layout instance</param>
            <returns>Copy of the handlers class is using</returns>
        </member>
        <member name="M:FileHelpers.RecordOperations.StartsWithIgnoringWhiteSpaces(System.String,System.String,System.StringComparison)">
            <summary>
            Determines whether the beginning of this string instance matches the specified string ignoring white spaces at the start.
            </summary>
            <param name="source">source string.</param>
            <param name="value">The string to compare.</param>
            <param name="comparisonType">string comparison type.</param>
            <returns></returns>
        </member>
        <member name="T:FileHelpers.CreateAndAssignDelegate">
            <summary>
            Create an object and assign
            </summary>
            <param name="values">Values that are assigned to instance</param>
            <returns>Instance of object initialised</returns>
        </member>
        <member name="T:FileHelpers.AssignDelegate">
            <summary>
            Assign values to a known object
            </summary>
            <param name="record">Record to assign</param>
            <param name="values">Values to assign to the record</param>
        </member>
        <member name="T:FileHelpers.ObjectToValuesDelegate">
            <summary>
            Extract the values from the object (function created based on FieldInfo
            </summary>
            <param name="record">instance to extract from</param>
            <returns>Values from the instance</returns>
        </member>
        <member name="T:FileHelpers.CreateObjectDelegate">
            <summary>
            Create an object (function created based upon a type)
            </summary>
            <returns>New object</returns>
        </member>
        <member name="T:FileHelpers.ReflectionHelper">
            <summary>
            Creates a series of functions that directly access
            the classes rather than creating all the access methods
            every time we access data.
            </summary>
        </member>
        <member name="M:FileHelpers.ReflectionHelper.ObjectToValuesMethod(System.Type,System.Reflection.FieldInfo[])">
            <summary>
            Create a delegate based on the field information that
            converts an object into an array of fields
            </summary>
            <param name="recordType">Object type to be processed</param>
            <param name="fields">Fields we are extracting</param>
            <returns>Delegate to convert object to fields</returns>
        </member>
        <member name="M:FileHelpers.ReflectionHelper.GetDefaultConstructor(System.Type)">
            <summary>
            Get the default constructor for the class
            </summary>
            <param name="recordType">class we are working on</param>
            <returns>Constructor information</returns>
        </member>
        <member name="M:FileHelpers.ReflectionHelper.CreateAndAssignValuesMethod(System.Type,System.Reflection.FieldInfo[])">
            <summary>
            Create a dynamic function that assigns fields to
            the record
            </summary>
            <param name="recordType">class we are basing assignment on</param>
            <param name="fields">LIst of fields to assign</param>
            <returns>Function to create object and perform assignment</returns>
        </member>
        <member name="M:FileHelpers.ReflectionHelper.AssignValuesMethod(System.Type,System.Reflection.FieldInfo[])">
            <summary>
            Assign fields to record type
            </summary>
            <param name="recordType">Class to base function on</param>
            <param name="fields">List of fields to assign</param>
            <returns>function that assigns only</returns>
        </member>
        <member name="M:FileHelpers.ReflectionHelper.CreateFastConstructor(System.Type)">
            <summary>
            Create a function that will create an instance of the class
            </summary>
            <param name="recordType">class we want to create an instance of</param>
            <returns>Function to create an instance</returns>
        </member>
        <member name="M:FileHelpers.ReflectionHelper.RecursiveGetFields(System.Type)">
            <summary>
            Get all the FieldInfo details from the class and optionally it's parents
            </summary>
            <param name="currentType">Type we are interrogating</param>
            <returns>IEnumerable list of fields in type</returns>
        </member>
        <member name="T:FileHelpers.Options.CsvOptions">
            <summary>Class used to pass information to the <see cref="!:FileHelpers.Dynamic.CsvClassBuilder"/> and the <see cref="!:CsvEngine"/></summary>
        </member>
        <member name="M:FileHelpers.Options.CsvOptions.#ctor(System.String,System.Char,System.Int32)">
            <summary>Create a CSV Wrapper using the specified number of fields.</summary>
            <param name="className">The name of the record class</param>
            <param name="delimiter">The delimiter for each field</param>
            <param name="numberOfFields">The number of fields of each record</param>
        </member>
        <member name="M:FileHelpers.Options.CsvOptions.#ctor(System.String,System.Char,System.Int32,System.Int32)">
            <summary>Create a CSV Wrapper using the specified number of fields.</summary>
            <param name="className">The name of the record class</param>
            <param name="delimiter">The delimiter for each field</param>
            <param name="numberOfFields">The number of fields of each record</param>
            <param name="headerLines">The number of lines to use as header</param>
        </member>
        <member name="M:FileHelpers.Options.CsvOptions.#ctor(System.String,System.Char,System.String)">
            <summary>Create a CSV Wrapper using the specified sample file with their headers.</summary>
            <param name="className">The name of the record class</param>
            <param name="delimiter">The delimiter for each field</param>
            <param name="sampleFile">A sample file with a header that contains the names of the fields.</param>
        </member>
        <member name="M:FileHelpers.Options.CsvOptions.#ctor(System.String,System.Char,System.Char,System.String)">
            <summary>Create a CSV Wrapper using the specified sample file with their headers.</summary>
            <param name="className">The name of the record class</param>
            <param name="delimiter">The delimiter for each field</param>
            <param name="sampleFile">A sample file with a header that contains the names of the fields.</param>
            <param name="headerDelimiter">The delimiter for the header line</param>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.SampleFileName">
            <summary>A sample file from where to read the field names and number.</summary>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.Delimiter">
            <summary>The delimiter for each field.</summary>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.HeaderDelimiter">
            <summary>The delimiter for each file name in the header.</summary>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.RecordClassName">
            <summary>The name used for the record class (a valid .NET class).</summary>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.FieldsPrefix">
            <summary>The prefix used when you only specified the number of fields</summary>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.NumberOfFields">
            <summary>The number of fields that the file contains.</summary>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.HeaderLines">
            <summary>The number of header lines</summary>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.IncludeHeaderNames">
            <summary> 
            If True, add to the first row the names of the columns taken from DataTable and 
            also update the value of HeaderLines option to 1 
            </summary> 
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.DateFormat">
            <summary>The DateFormat used to read and write DateTime values</summary>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.DecimalSeparator">
            <summary>The Decimal Separator used to read and write doubles, singles and decimal values</summary>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.Encoding">
            <summary>
            Encoding used when handling the CSV files.
            </summary>
        </member>
        <member name="P:FileHelpers.Options.CsvOptions.IgnoreEmptyLines">
            <summary>Should blank lines in the source file be left out of the final result?</summary>
        </member>
        <member name="M:FileHelpers.Options.CsvOptions.ValueToString(System.Object)">
            <summary>
            Convert a field to a string
            </summary>
            <param name="o">object we want to convert</param>
            <returns>string representation of the string</returns>
        </member>
        <member name="T:FileHelpers.Options.DelimitedRecordOptions">
            <summary>
            This class allows you to set some options of the delimited records
            at runtime. With options the library is more flexible.
            </summary>
        </member>
        <member name="M:FileHelpers.Options.DelimitedRecordOptions.#ctor(FileHelpers.IRecordInfo)">
            <summary>
            This class allows you to set some options of the delimited records
            at runtime. With options the library is more flexible.
            </summary>
            <param name="info">Record information</param>
        </member>
        <member name="P:FileHelpers.Options.DelimitedRecordOptions.Delimiter">
            <summary>
            The delimiter used to identify each field in the data.
            </summary>
        </member>
        <member name="T:FileHelpers.Options.FixedRecordOptions">
            <summary>
            This class allows you to set some options of the fixed length records
            but at runtime.
            With this options the library is more flexible than never.
            </summary>
        </member>
        <member name="M:FileHelpers.Options.FixedRecordOptions.#ctor(FileHelpers.IRecordInfo)">
            <summary>
            This class allows you to set some options of the fixed length
            records but at runtime.
            With this options the library is more flexible than never.
            </summary>
            <param name="info">Record information</param>
        </member>
        <member name="P:FileHelpers.Options.FixedRecordOptions.FixedMode">
            <summary>
            Indicates the behavior when variable length records are found in a
            [<see cref="T:FileHelpers.FixedLengthRecordAttribute"/>]. (Note: nothing in
            common with [FieldOptional])
            </summary>
        </member>
        <member name="P:FileHelpers.Options.FixedRecordOptions.RecordLength">
            <summary>
            The sum of the individual field lengths.
            </summary>
        </member>
        <member name="T:FileHelpers.Options.RecordOptions">
            <summary>
            This class allows you to set some options of the records at runtime.
            With these options the library is now more flexible than ever.
            </summary>
        </member>
        <member name="M:FileHelpers.Options.RecordOptions.#ctor(FileHelpers.IRecordInfo)">
            <summary>
            This class allows you to set some options of the records at runtime.
            With these options the library is now more flexible than ever.
            </summary>
            <param name="info">Record information</param>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.Fields">
            <summary>
            Copies the fields in the current recordinfo.
            </summary>
        </member>
        <member name="M:FileHelpers.Options.RecordOptions.RemoveField(System.String)">
            <summary>
            Removes the filed from the underlying <seealso cref="T:FileHelpers.IRecordInfo"/>.
            </summary>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.FieldCount">
            <summary>
            The number of fields of the record type.
            </summary>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.FieldsNames">
            <summary>
            Returns an string array with the fields names.
            Note : Do NOT change the values of the array, clone it first if needed
            </summary>
            <returns>An string array with the fields names.</returns>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.FieldsTypes">
            <summary>
            Returns a Type[] array with the fields types.
            Note : Do NOT change the values of the array, clone it first if
            needed
            </summary>
            <returns>An Type[] array with the fields types.</returns>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.IgnoreFirstLines">
            <summary>
            Indicates the number of first lines to be discarded.
            </summary>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.IgnoreLastLines">
            <summary>
            Indicates the number of lines at the end of file to be discarded.
            </summary>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.IgnoreEmptyLines">
            <summary>
            Indicates that the engine must ignore the empty lines while
            reading.
            </summary>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.RecordCondition">
            <summary>
            Used to tell the engine which records must be included or excluded
            while reading.
            </summary>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.IgnoreCommentedLines">
            <summary>
            Indicates that the engine must ignore the lines with this comment
            marker.
            </summary>
        </member>
        <member name="T:FileHelpers.Options.RecordOptions.RecordConditionInfo">
            <summary>
            Used to tell the engine which records must be included or excluded
            while reading.
            </summary>
        </member>
        <member name="M:FileHelpers.Options.RecordOptions.RecordConditionInfo.#ctor(FileHelpers.IRecordInfo)">
            <summary>
            Used to tell the engine which records must be included or
            excluded while reading.
            </summary>
            <param name="ri">Record information</param>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.RecordConditionInfo.Condition">
            <summary>
            The condition used to include or exclude records.
            </summary>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.RecordConditionInfo.Selector">
            <summary>
            The selector used by the <see cref="P:FileHelpers.Options.RecordOptions.RecordCondition"/>.
            </summary>
        </member>
        <member name="T:FileHelpers.Options.RecordOptions.IgnoreCommentInfo">
            <summary>
            Indicates that the engine must ignore the lines with this comment
            marker.
            </summary>
        </member>
        <member name="M:FileHelpers.Options.RecordOptions.IgnoreCommentInfo.#ctor(FileHelpers.IRecordInfo)">
            <summary>
            Indicates that the engine must ignore the lines with this
            comment marker.
            </summary>
            <param name="ri">Record information</param>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.IgnoreCommentInfo.CommentMarker">
            <summary>
            <para>Indicates that the engine must ignore the lines with this
            comment marker.</para>
            <para>An empty string or null indicates that the engine doesn't
            look for comments</para>
            </summary>
        </member>
        <member name="P:FileHelpers.Options.RecordOptions.IgnoreCommentInfo.InAnyPlace">
            <summary>
            Indicates if the comment can have spaces or tabs at left (true
            by default)
            </summary>
        </member>
        <member name="M:FileHelpers.Options.RecordOptions.RecordToString(System.Object)">
            <summary>
            Allows the creating of a record string of the given record. Is
            useful when your want to log errors to a plan text file or database
            </summary>
            <param name="record">
            The record that will be transformed to string
            </param>
            <returns>The string representation of the current record</returns>
        </member>
        <member name="M:FileHelpers.Options.RecordOptions.RecordToValues(System.Object)">
            <summary>
            Allows to get an object[] with the values of the fields in the <paramref name="record"/>
            </summary>
            <param name="record">The record that will be transformed to object[]</param>
            <returns>The object[] with the values of the fields in the current record</returns>
        </member>
        <member name="M:FileHelpers.Options.RecordOptions.PositiveValue(System.Int32)">
            <summary>
            Check an integer value is positive (0 or greater)
            </summary>
            <param name="val">Integer to test</param>
        </member>
        <member name="T:FileHelpers.Options.FieldBaseCollection">
            <summary>An amount of <seealso cref="T:FileHelpers.FieldBase"/>.</summary>
        </member>
        <member name="T:FileHelpers.Dynamic.StringHelper">
            <summary>
            Helper classes for strings
            </summary>
        </member>
        <member name="M:FileHelpers.Dynamic.StringHelper.ReplaceRecursive(System.String,System.String,System.String)">
            <summary>
            replace the one string with another, and keep doing it
            </summary>
            <param name="original">Original string</param>
            <param name="oldValue">Value to replace</param>
            <param name="newValue">value to replace with</param>
            <returns>String with all multiple occurrences replaced</returns>
        </member>
        <member name="M:FileHelpers.Dynamic.StringHelper.ToValidIdentifier(System.String)">
            <summary>
            convert a string into a valid identifier
            </summary>
            <param name="original">Original string</param>
            <returns>valid identifier  Original_string</returns>
        </member>
        <member name="T:FileHelpers.Dynamic.ValidIdentifierValidator">
            <summary>
            Validate that the identifier is valid for the language
            </summary>
        </member>
        <member name="M:FileHelpers.Dynamic.ValidIdentifierValidator.ValidIdentifier(System.String)">
            <summary>
            Validate that the identifier is valid for the language
            </summary>
            <param name="id">Name of identifier</param>
            <returns>Is Valid</returns>
        </member>
        <member name="M:FileHelpers.Dynamic.ValidIdentifierValidator.ValidIdentifier(System.String,System.Boolean)">
            <summary>
            Validate that the identifier is valid for the language
            </summary>
            <param name="id">Name of identifier</param>
            <param name="isType">Is it a type statement, allows for dots, ? for nullable, etc</param>
            <returns>Is Valid</returns>
        </member>
        <member name="T:FileHelpers.DelimitedFileEngine">
            <summary>
            This version of the <see cref="T:FileHelpers.FileHelperEngine"/> is exclusively
            for delimited records. It allows you to change the delimiter and
            other options at runtime
            </summary>
            <remarks>
            Useful when you need to export or import the same info with 2 or
            more different delimiters or slightly different options.
            </remarks>
        </member>
        <member name="M:FileHelpers.DelimitedFileEngine.#ctor(System.Type)">
            <summary>
            Create a version of the <see cref="T:FileHelpers.FileHelperEngine"/> exclusively
            for delimited records. It allows you to change the delimiter and
            other options at runtime
            </summary>
            <remarks>
            Useful when you need to export or import the same info with 2 or
            more different delimiters or slightly different options.
            </remarks>
            <param name="recordType">The record mapping class.</param>
        </member>
        <member name="M:FileHelpers.DelimitedFileEngine.#ctor(System.Type,System.Text.Encoding)">
            <summary>
            Create a delimited engine of record type and encoding
            </summary>
            <param name="recordType">Type of record to read</param>
            <param name="encoding">Encoding of each record</param>
        </member>
        <member name="P:FileHelpers.DelimitedFileEngine.Options">
            <summary>
            Allow changes in the record layout like delimiters and others
            common settings.
            </summary>
        </member>
        <member name="T:FileHelpers.DelimitedFileEngine`1">
            <summary>
            A version of the <see cref="T:FileHelpers.FileHelperEngine"/> exclusively for 
            delimited records. It allows you to change the delimiter and other
            options at runtime
            </summary>
            <remarks>
            Useful when you need to export or import the same info with 2 or more
            more different delimiters or slightly different options.
            </remarks>
        </member>
        <member name="M:FileHelpers.DelimitedFileEngine`1.#ctor">
            <summary>
            Create a version of the <see cref="T:FileHelpers.FileHelperEngine"/> exclusively
            for delimited records. It allows you to change the delimiter and
            other options at runtime
            </summary>
            <remarks>
            Useful when you need to export or import the same info with 2 or
            more different delimiters or slightly different options.
            </remarks>
        </member>
        <member name="M:FileHelpers.DelimitedFileEngine`1.#ctor(System.Text.Encoding)">
            <summary>
            Create a Delimited engine with file type of Encoding
            </summary>
            <param name="encoding">Type of encoding on file</param>
        </member>
        <member name="P:FileHelpers.DelimitedFileEngine`1.Options">
            <summary>
            Allows changes in the record layout like delimiters and others
            common settings.
            </summary>
        </member>
        <member name="T:FileHelpers.EngineBase">
            <summary>Abstract Base class for the engines of the library: 
            <see cref="T:FileHelpers.FileHelperEngine"/> and 
            <see cref="T:FileHelpers.FileHelperAsyncEngine"/></summary>
        </member>
        <member name="M:FileHelpers.EngineBase.#ctor(System.Type)">
            <summary>
            Create an engine on record type, with default encoding
            </summary>
            <param name="recordType">Class to base engine on</param>
        </member>
        <member name="M:FileHelpers.EngineBase.#ctor(System.Type,System.Text.Encoding)">
            <summary>
            Create and engine on type with specified encoding
            </summary>
            <param name="recordType">Class to base engine on</param>
            <param name="encoding">encoding of the file</param>
        </member>
        <member name="M:FileHelpers.EngineBase.#ctor(FileHelpers.RecordInfo)">
            <summary>
            Create an engine on the record info provided
            </summary>
            <param name="ri">Record information</param>
        </member>
        <member name="P:FileHelpers.EngineBase.LineNumber">
            <summary>The current line number.</summary>
        </member>
        <member name="P:FileHelpers.EngineBase.TotalRecords">
            <summary>The total numbers of records in the last read/written file
		       (only works with whole read/write).</summary>
        </member>
        <member name="M:FileHelpers.EngineBase.GetFileHeader">
            <summary>
            Builds a line with the name of the fields, for a delimited files it
            uses the same delimiter, for a fixed length field it writes the
            fields names separated with tabs
            </summary>
            <returns>field names structured for the heading of the file</returns>
        </member>
        <member name="P:FileHelpers.EngineBase.RecordType">
            <summary>Returns the type of records handled by this engine.</summary>
        </member>
        <member name="P:FileHelpers.EngineBase.HeaderText">
            <summary>The Read Header in the last Read operation. If any.</summary>
        </member>
        <member name="F:FileHelpers.EngineBase.mFooterText">
            <summary>The Read Footer in the last Read operation. If any.</summary>
        </member>
        <member name="P:FileHelpers.EngineBase.FooterText">
            <summary>The Read Footer in the last Read operation. If any.</summary>
        </member>
        <member name="F:FileHelpers.EngineBase.mEncoding">
            <summary>
            The encoding to Read and Write the streams. 
            Default is the system's current ANSI code page.
            </summary>
        </member>
        <member name="P:FileHelpers.EngineBase.Encoding">
            <summary>
            The encoding to Read and Write the streams. 
            Default is the system's current ANSI code page.
            </summary>
            <value>Default is the system's current ANSI code page.</value>
        </member>
        <member name="F:FileHelpers.EngineBase.mNewLineForWrite">
            <summary>
            Newline string to be used when engine writes to file. 
            Default is the system's newline setting (System.Environment.NewLine).
            </summary>
        </member>
        <member name="P:FileHelpers.EngineBase.NewLineForWrite">
            <summary>
            Newline string to be used when engine writes to file. 
            Default is the system's newline setting (System.Environment.NewLine).
            </summary>
            <value>Default is the system's newline setting.</value>
        </member>
        <member name="F:FileHelpers.EngineBase.mErrorManager">
            <summary>This is a common class that manage the errors of the library.</summary>
        </member>
        <member name="P:FileHelpers.EngineBase.ErrorManager">
            <summary>This is a common class that manages the errors of the library.</summary>
            <remarks>
              You can find complete information about the errors encountered while processing.
              For example, you can get the errors, their number and save them to a file, etc.
              </remarks>
              <seealso cref="T:FileHelpers.ErrorManager"/>
        </member>
        <member name="P:FileHelpers.EngineBase.ErrorMode">
            <summary>
            Indicates the behavior of the engine when it finds an error.
            {Shortcut for <seealso cref="P:FileHelpers.ErrorManager.ErrorMode"/>)
            </summary>
        </member>
        <member name="M:FileHelpers.EngineBase.ResetFields">
            <summary>
            Reset back to the beginning
            </summary>
        </member>
        <member name="E:FileHelpers.EngineBase.Progress">
            <summary>Event handler called to notify progress.</summary>
        </member>
        <member name="P:FileHelpers.EngineBase.MustNotifyProgress">
            <summary>
            Determine whether a progress call is needed
            </summary>
        </member>
        <member name="M:FileHelpers.EngineBase.OnProgress(FileHelpers.Events.ProgressEventArgs)">
            <summary>
            Raises the Progress Event
            </summary>
            <param name="e">The Event Args</param>
        </member>
        <member name="P:FileHelpers.EngineBase.Options">
            <summary>
            Allows you to change some record layout options at runtime
            </summary>
        </member>
        <member name="T:FileHelpers.EventEngineBase`1">
            <summary>
            Base for engine events
            </summary>
            <typeparam name="T">Specific engine</typeparam>
        </member>
        <member name="M:FileHelpers.EventEngineBase`1.#ctor(System.Type)">
            <summary>
            Define an event based on an engine, based on a record
            </summary>
            <param name="recordType">Type of the record</param>
        </member>
        <member name="M:FileHelpers.EventEngineBase`1.#ctor(System.Type,System.Text.Encoding)">
            <summary>
            Define an event based on a record with a specific encoding
            </summary>
            <param name="recordType">Type of the record</param>
            <param name="encoding">Encoding specified</param>
        </member>
        <member name="M:FileHelpers.EventEngineBase`1.#ctor(FileHelpers.RecordInfo)">
            <summary>
            Event based upon supplied record information
            </summary>
            <param name="ri"></param>
        </member>
        <member name="E:FileHelpers.EventEngineBase`1.BeforeReadRecord">
            <summary>
            Called in read operations just before the record string is
            translated to a record.
            </summary>
        </member>
        <member name="E:FileHelpers.EventEngineBase`1.AfterReadRecord">
            <summary>
            Called in read operations just after the record was created from a
            record string.
            </summary>
        </member>
        <member name="E:FileHelpers.EventEngineBase`1.BeforeWriteRecord">
            <summary>
            Called in write operations just before the record is converted to a
            string to write it.
            </summary>
        </member>
        <member name="E:FileHelpers.EventEngineBase`1.AfterWriteRecord">
            <summary>
            Called in write operations just after the record was converted to a
            string.
            </summary>
        </member>
        <member name="P:FileHelpers.EventEngineBase`1.MustNotifyRead">
            <summary>
            Check whether we need to notify the read to anyone
            </summary>
        </member>
        <member name="P:FileHelpers.EventEngineBase`1.MustNotifyWrite">
            <summary>
            Determine whether we have to run notify write on every iteration.
            </summary>
        </member>
        <member name="M:FileHelpers.EventEngineBase`1.OnBeforeReadRecord(FileHelpers.Events.BeforeReadEventArgs{`0})">
            <summary>
            Provide a hook to preprocess a record
            </summary>
            <param name="e">Record details before read</param>
            <returns>True if record to be skipped</returns>
        </member>
        <member name="M:FileHelpers.EventEngineBase`1.OnAfterReadRecord(System.String,`0,System.Boolean,System.Int32)">
            <summary>
            Post process a record
            </summary>
            <param name="line">Record read</param>
            <param name="record">Type of record</param>
            <param name="lineChanged">Has the line been updated so that the engine switches to this version</param>
            <param name="lineNumber">Number of line in file</param>
            <returns>true if record to be skipped</returns>
        </member>
        <member name="M:FileHelpers.EventEngineBase`1.OnBeforeWriteRecord(`0,System.Int32)">
            <summary>
            Before a write is executed perform this check to see
            if we want to modify or reject the record.
            </summary>
            <param name="record">Instance to process</param>
            <param name="lineNumber">Number of line within file</param>
            <returns>true if record is to be dropped</returns>
        </member>
        <member name="M:FileHelpers.EventEngineBase`1.OnAfterWriteRecord(System.String,`0)">
            <summary>
            After we have written a record,  do we want to process it.
            </summary>
            <param name="line">Line that will be output</param>
            <param name="record">Record we are processing</param>
            <returns>Record to be written</returns>
        </member>
        <member name="T:FileHelpers.FileHelperEngine">
            <summary>
            Basic engine to read record by record
            </summary>
        </member>
        <member name="M:FileHelpers.FileHelperEngine.#ctor(System.Type)">
            <summary>
			 Initializes a new instance of the FileHelperEngine class with the specified type of records.
		</summary>
        </member>
        <member name="M:FileHelpers.FileHelperEngine.#ctor(System.Type,System.Text.Encoding)">
            <summary>
			 Initializes a new instance of the FileHelperEngine class with the specified type of records.
		</summary>
            <param name="recordType">The record mapping class.</param>
            <param name="encoding">The Encoding used by the engine.</param>
        </member>
        <member name="T:FileHelpers.FileHelperEngine`1">
            <summary>
			<para><b>One of the main classes of the library.</b></para>
			<para>This engine is responsible to Read/Write the records <b>at once</b> from/to files or
				streams.</para>
			<para>You can use the <see cref="T:FileHelpers.DelimitedFileEngine" /> or the <see cref="T:FileHelpers.FixedFileEngine" /> if
				you need to change some options at runtime</para>
		</summary><remarks>
		 <para>You can set the <see cref="P:FileHelpers.ErrorManager.ErrorMode" /> of this class when found an error, and can
			 retrieve them with the <see cref="P:FileHelpers.ErrorManager.Errors" />
			 property.</para>
		 <para>See in the <a href="http://www.filehelpers.net/diagrams">Class Diagram</a> and in the
			<a href="http://www.filehelpers.net/quickstart/">Quick Start Guide</a> for more Info.</para>
		 <para>Or you can browse the <a href="http://www.filehelpers.net/examples/">Examples section</a> for more code.</para>
		 <para>Engines Diagram:</para>
		 <para><center><img src="/images/diag_engines.png" /></center></para>
		</remarks><seealso href="http://www.filehelpers.net/quickstart/">Quick Start Guide</seealso><seealso href="http://www.filehelpers.net/diagrams">Class Diagram</seealso><seealso href="http://www.filehelpers.net/examples/">Examples of Use</seealso><seealso href="http://www.filehelpers.net/mustread">Attributes List</seealso>
            <example>
			This example shows a simple example of use of the library with a minimum of code:
<code>

	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;());

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}

	
	public void WriteExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.WriteFile("destination.txt", records);
		
		// Now the file contains the created record in this format:
		// 
		// Hello World,12
		
	}


</code>		</example>
            <typeparam name="T">The record type.</typeparam>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.#ctor">
            <summary>
			 Initializes a new instance of the FileHelperEngine class with the specified type of records.
		</summary>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.#ctor(System.Text.Encoding)">
            <summary>
			 Initializes a new instance of the FileHelperEngine class with the specified type of records.
		</summary>
            <param name="encoding">The Encoding used by the engine.</param>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.#ctor(System.Type,System.Text.Encoding)">
            <summary>
			 Initializes a new instance of the FileHelperEngine class with the specified type of records.
		</summary>
            <param name="encoding">The Encoding used by the engine.</param>
            <param name="recordType">Type of record we are reading</param>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadFile(System.String)">
            <summary>
		Read a file and return an array of the contained records.
		</summary><remarks>
		This method opens, reads and closes the file (don't open or close the file before or after calling this method)
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="fileName">The file path to be read.</param><returns>An array of the records in the file</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadFile(System.String,System.Int32)">
            <summary>
		Read a file and return an array of the contained records.
		</summary><remarks>
		This method opens, reads and closes the file (don't open or close the file before or after calling this method)
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="fileName">The file path to be read.</param><returns>An array of the records in the file</returns>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadFileAsList(System.String)">
            <summary>
		Read a file and return an array of the contained records.
		</summary><remarks>
		This method opens, reads and closes the file (don't open or close the file before or after calling this method)
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="fileName">The file path to be read.</param><returns>An array of the records in the file</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadFileAsList(System.String,System.Int32)">
            <summary>
		Read a file and return an array of the contained records.
		</summary><remarks>
		This method opens, reads and closes the file (don't open or close the file before or after calling this method)
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="fileName">The file path to be read.</param><returns>An array of the records in the file</returns>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadStream(System.IO.TextReader)">
            <summary>
		Read a Stream and return an array of the contained records.
		</summary><remarks>
		This method uses the stream and closes it after using it.
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="reader">The reader of the source stream.</param><returns>An array of the records in the Stream</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadStream(System.IO.TextReader,System.Int32)">
            <summary>
		Read a Stream and return an array of the contained records.
		</summary><remarks>
		This method uses the stream and closes it after using it.
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="reader">The reader of the source stream.</param><returns>An array of the records in the Stream</returns>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadStreamAsList(System.IO.TextReader,System.Int32)">
            <summary>
		Read a Stream and return an array of the contained records.
		</summary><remarks>
		This method uses the stream and closes it after using it.
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="reader">The reader of the source stream.</param><returns>An array of the records in the Stream</returns>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadString(System.String)">
            <summary>
		Read a String and return an array of the contained records.
		</summary><param name="source">The string that contains the records.</param><returns>An array of the records in the String.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadString(System.String,System.Int32)">
            <summary>
		Read a String and return an array of the contained records.
		</summary><param name="source">The string that contains the records.</param><returns>An array of the records in the String.</returns>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadStringAsList(System.String)">
            <summary>
		Read a String and return an array of the contained records.
		</summary><param name="source">The string that contains the records.</param><returns>An array of the records in the String.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadStringAsList(System.String,System.Int32)">
            <summary>
		Read a String and return an array of the contained records.
		</summary><param name="source">The string that contains the records.</param><returns>An array of the records in the String.</returns>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.WriteFile(System.String,System.Collections.Generic.IEnumerable{`0})">
            <summary>
		Write an array of records to the specified file.
		</summary><remarks>
			<para>This method opens, writes and closes the file
			(don't open or close the file before or after calling this method)</para>
		<para>This method over writes any existing files.</para>
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void WriteExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.WriteFile("destination.txt", records);
		
		// Now the file contains the created record in this format:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="fileName">The file path to be write.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.WriteFile(System.String,System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
		Write the specified number of records from the array to a file.
		</summary><remarks>
		<para>This method opens, writes and closes the file (don't open
			or close the file before or after calling this
			method)</para>
		<para>This method over writes any existing files.</para>
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void WriteExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.WriteFile("destination.txt", records);
		
		// Now the file contains the created record in this format:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="fileName">The file path to be write.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.WriteStream(System.IO.TextWriter,System.Collections.Generic.IEnumerable{`0})">
            <summary>
		Write an array of records to the specified Stream.
		</summary><remarks>
		This method only uses the stream and does not close it, you must do it.
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void WriteExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.WriteFile("destination.txt", records);
		
		// Now the file contains the created record in this format:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="writer">The writer of the source stream.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.WriteStream(System.IO.TextWriter,System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
		Write the specified number of records in the array to the Stream.
		</summary><remarks>
		This method only uses the stream and does not close it, you must do it.
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void WriteExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.WriteFile("destination.txt", records);
		
		// Now the file contains the created record in this format:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="writer">The writer of the source stream.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.WriteString(System.Collections.Generic.IEnumerable{`0})">
            <summary>
		Write an array of records to an String and return it.
		</summary><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>The resulting string after write the records.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.WriteString(System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
		Write an array of records to an String and return it.
		</summary><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>The resulting string after write the records.</returns><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.AppendToFile(System.String,`0)">
            <summary>
		Append a record to the specified file.
		</summary><remarks>
			This method opens, seeks to the end, writes and closes the file
			(don't open or close the file before or after calling this method)
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void AppendExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.AppendToFile("destination.txt", records);
		
		// Now the file contains have one more record at the end:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="fileName">The file path to be written at end.</param><param name="record">The record to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.AppendToFile(System.String,System.Collections.Generic.IEnumerable{`0})">
            <summary>
		Append an array of records to the specified file.
		</summary><remarks>
			This method opens, seeks to the end, writes and closes the file
			(don't open or close the file before or after calling this method)
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void AppendExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.AppendToFile("destination.txt", records);
		
		// Now the file contains have one more record at the end:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="fileName">The file path to be appended to.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadFileAsDT(System.String)">
            <summary>
            Read the records of the file and fill a DataTable with them
            </summary>
            <param name="fileName">The file name.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadFileAsDT(System.String,System.Int32)">
            <summary>
            Read the records of the file and fill a DataTable with them
            </summary>
            <param name="fileName">The file name.</param>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadStringAsDT(System.String)">
            <summary>
            Read the records of a string and fill a DataTable with them.
            </summary>
            <param name="source">The source string with the records.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadStringAsDT(System.String,System.Int32)">
            <summary>
            Read the records of a string and fill a DataTable with them.
            </summary>
            <param name="source">The source string with the records.</param>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadStreamAsDT(System.IO.TextReader)">
            <summary>
            Read the records of the stream and fill a DataTable with them
            </summary>
            <param name="reader">The stream with the source records.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="M:FileHelpers.FileHelperEngine`1.ReadStreamAsDT(System.IO.TextReader,System.Int32)">
            <summary>
            Read the records of the stream and fill a DataTable with them
            </summary>
            <param name="reader">The stream with the source records.</param>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="T:FileHelpers.FixedFileEngine">
            <summary>
            This version of the <see cref="T:FileHelpers.FileHelperEngine"/> is exclusively for 
            fixed length records. It allows you to change options at runtime
            </summary>
            <remarks>
            Useful when you need to export or import the same info with slightly
            different options.
            </remarks>
        </member>
        <member name="M:FileHelpers.FixedFileEngine.#ctor(System.Type)">
            <summary>
            This version of the <see cref="T:FileHelpers.FileHelperEngine"/> is exclusively for 
            fixed length records. It allows you to change options at runtime
            </summary>
            <remarks>
            Useful when you need to export or import the same info with
            slightly different options.
            </remarks>
            <param name="recordType">The record mapping class.</param>
        </member>
        <member name="M:FileHelpers.FixedFileEngine.#ctor(System.Type,System.Text.Encoding)">
            <summary>
            Read a record with fixed length fields
            </summary>
            <param name="recordType">record type to read</param>
            <param name="encoding">Encoding to use</param>
        </member>
        <member name="P:FileHelpers.FixedFileEngine.Options">
            <summary>Allow changes some fixed length options and others common settings.</summary>
        </member>
        <member name="T:FileHelpers.FixedFileEngine`1">
            <summary>
            This version of the <see cref="T:FileHelpers.FileHelperEngine"/> is exclusively for 
            fixed length records. It allows you to change options at runtime
            </summary>
            <remarks>
            Useful when you need to export or import the same info with slightly
            different options.
            </remarks>
        </member>
        <member name="M:FileHelpers.FixedFileEngine`1.#ctor">
            <summary>
            Creates a version of the <see cref="T:FileHelpers.FileHelperEngine"/> exclusively
            for fixed length records that allow you to change options at runtime
            </summary>
            <remarks>
            Useful when you need to export or import the same info with
            slightly different options.
            </remarks>
        </member>
        <member name="M:FileHelpers.FixedFileEngine`1.#ctor(System.Text.Encoding)">
            <summary>
            Creates a version of the <see cref="T:FileHelpers.FileHelperEngine"/> exclusively
            for fixed length records that allow you to change options at runtime
            </summary>
            <remarks>
            Useful when you need to export or import the same info with
            slightly different options.
            </remarks>
            <param name="encoding">Encoding of file to be read</param>
        </member>
        <member name="P:FileHelpers.FixedFileEngine`1.Options">
            <summary>Allow changes some fixed length options and others common settings.</summary>
        </member>
        <member name="T:FileHelpers.IFileHelperEngine`1">
            <summary>
            Interface for The fileHelpers generic engine
            </summary>
            <typeparam name="T">Type of object array to return</typeparam>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadFile(System.String)">
            <summary>
		Read a file and return an array of the contained records.
		</summary><remarks>
		This method opens, reads and closes the file (don't open or close the file before or after calling this method)
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="fileName">The file path to be read.</param><returns>An array of the records in the file</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadFile(System.String,System.Int32)">
            <summary>
		Read a file and return an array of the contained records.
		</summary><remarks>
		This method opens, reads and closes the file (don't open or close the file before or after calling this method)
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="fileName">The file path to be read.</param><returns>An array of the records in the file</returns>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadStream(System.IO.TextReader)">
            <summary>
		Read a Stream and return an array of the contained records.
		</summary><remarks>
		This method uses the stream and closes it after using it.
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="reader">The reader of the source stream.</param><returns>An array of the records in the Stream</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadStream(System.IO.TextReader,System.Int32)">
            <summary>
		Read a Stream and return an array of the contained records.
		</summary><remarks>
		This method uses the stream and closes it after using it.
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void ReadExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();

		var records = engine.ReadFile("source.txt");
		
		// Now "records" array contains all the records in the
		// sourcefile and can be acceded like this:
		
		int sum = records[0].Field2 + records[1].Field2;
	}


		</code>
		</example><param name="reader">The reader of the source stream.</param><returns>An array of the records in the Stream</returns>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadString(System.String)">
            <summary>
		Read a String and return an array of the contained records.
		</summary><param name="source">The string that contains the records.</param><returns>An array of the records in the String.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadString(System.String,System.Int32)">
            <summary>
		Read a String and return an array of the contained records.
		</summary><param name="source">The string that contains the records.</param><returns>An array of the records in the String.</returns>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.WriteFile(System.String,System.Collections.Generic.IEnumerable{`0})">
            <summary>
		Write an array of records to the specified file.
		</summary><remarks>
			<para>This method opens, writes and closes the file
			(don't open or close the file before or after calling this method)</para>
		<para>This method over writes any existing files.</para>
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void WriteExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.WriteFile("destination.txt", records);
		
		// Now the file contains the created record in this format:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="fileName">The file path to be write.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.WriteFile(System.String,System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
		Write the specified number of records from the array to a file.
		</summary><remarks>
		<para>This method opens, writes and closes the file (don't open
			or close the file before or after calling this
			method)</para>
		<para>This method over writes any existing files.</para>
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void WriteExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.WriteFile("destination.txt", records);
		
		// Now the file contains the created record in this format:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="fileName">The file path to be write.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.WriteStream(System.IO.TextWriter,System.Collections.Generic.IEnumerable{`0})">
            <summary>
		Write an array of records to the specified Stream.
		</summary><remarks>
		This method only uses the stream and does not close it, you must do it.
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void WriteExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.WriteFile("destination.txt", records);
		
		// Now the file contains the created record in this format:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="writer">The writer of the source stream.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.WriteStream(System.IO.TextWriter,System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
		Write the specified number of records in the array to the Stream.
		</summary><remarks>
		This method only uses the stream and does not close it, you must do it.
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void WriteExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.WriteFile("destination.txt", records);
		
		// Now the file contains the created record in this format:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="writer">The writer of the source stream.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.WriteString(System.Collections.Generic.IEnumerable{`0})">
            <summary>
		Write an array of records to an String and return it.
		</summary><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>The resulting string after write the records.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.WriteString(System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
		Write an array of records to an String and return it.
		</summary><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>The resulting string after write the records.</returns><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.AppendToFile(System.String,`0)">
            <summary>
		Append a record to the specified file.
		</summary><remarks>
			This method opens, seeks to the end, writes and closes the file
			(don't open or close the file before or after calling this method)
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void AppendExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.AppendToFile("destination.txt", records);
		
		// Now the file contains have one more record at the end:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="fileName">The file path to be written at end.</param><param name="record">The record to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.AppendToFile(System.String,System.Collections.Generic.IEnumerable{`0})">
            <summary>
		Append an array of records to the specified file.
		</summary><remarks>
			This method opens, seeks to the end, writes and closes the file
			(don't open or close the file before or after calling this method)
		</remarks><example>
			This example shows a simple example of use of the library with a minimum of code:
<code>


	using FileHelpers;
	
	// First declare the record class
		
	[Delimitedrecord("|")]
	public class SampleType
	{
		public string Field1;
		public int    Field2;
	}


	public void AppendExample()
	{
		var engine = new FileHelperEngine&lt;SampleType&gt;();
		
		SampleType[] records = new SampleType[1];

		records[0] = new SampleType();

		records[0].Field1 = "Hello World";
		records[0].Field2 = 12;

		engine.AppendToFile("destination.txt", records);
		
		// Now the file contains have one more record at the end:
		// 
		// Hello World,12
		
	}


		</code>
		</example><param name="fileName">The file path to be appended to.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadFileAsDT(System.String)">
            <summary>
            Read the records of the file and fill a DataTable with them
            </summary>
            <param name="fileName">The file name.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadFileAsDT(System.String,System.Int32)">
            <summary>
            Read the records of the file and fill a DataTable with them
            </summary>
            <param name="fileName">The file name.</param>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadStringAsDT(System.String)">
            <summary>
            Read the records of a string and fill a DataTable with them.
            </summary>
            <param name="source">The source string with the records.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadStringAsDT(System.String,System.Int32)">
            <summary>
            Read the records of a string and fill a DataTable with them.
            </summary>
            <param name="source">The source string with the records.</param>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadStreamAsDT(System.IO.TextReader)">
            <summary>
            Read the records of the stream and fill a DataTable with them
            </summary>
            <param name="reader">The stream with the source records.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="M:FileHelpers.IFileHelperEngine`1.ReadStreamAsDT(System.IO.TextReader,System.Int32)">
            <summary>
            Read the records of the stream and fill a DataTable with them
            </summary>
            <param name="reader">The stream with the source records.</param>
            <param name="maxRecords">The max number of records to read. Int32.MaxValue or -1 to read all records.</param>
            <returns>The DataTable with the read records.</returns>
        </member>
        <member name="E:FileHelpers.IFileHelperEngine`1.BeforeReadRecord">
            <summary>Called in read operations just before the record string is translated to a record.</summary>
        </member>
        <member name="E:FileHelpers.IFileHelperEngine`1.AfterReadRecord">
            <summary>Called in read operations just after the record was created from a record string.</summary>
        </member>
        <member name="E:FileHelpers.IFileHelperEngine`1.BeforeWriteRecord">
            <summary>Called in write operations just before the record is converted to a string to write it.</summary>
        </member>
        <member name="E:FileHelpers.IFileHelperEngine`1.AfterWriteRecord">
            <summary>Called in write operations just after the record was converted to a string.</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperEngine`1.Options">
            <summary>
            Allows to change some record layout options at runtime
            </summary>
        </member>
        <member name="P:FileHelpers.IFileHelperEngine`1.LineNumber">
            <summary>The current line number.</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperEngine`1.TotalRecords">
            <summary>The total numbers of records in the last read/written file
		       (only works with whole read/write).</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperEngine`1.RecordType">
            <summary>Returns the type of records handled by this engine.</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperEngine`1.HeaderText">
            <summary>The read header in the last read operation. If any.</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperEngine`1.FooterText">
            <summary>The read footer in the last read operation. If any.</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperEngine`1.NewLineForWrite">
            <summary>Newline char or string to be used when engine writes records.</summary>
        </member>
        <member name="P:FileHelpers.IFileHelperEngine`1.Encoding">
            <summary>
            The encoding to Read and Write the streams. Default is the system's
            current ANSI code page.
            </summary>
            <value>Default is the system's current ANSI code page.</value>
        </member>
        <member name="P:FileHelpers.IFileHelperEngine`1.ErrorManager">
            <summary>This is a common class that manage the errors of the library.</summary>
            <remarks>You can, for example, get the errors, their number, Save them to a file, etc.</remarks>
        </member>
        <member name="P:FileHelpers.IFileHelperEngine`1.ErrorMode">
            <summary>
            Indicates the behavior of the engine when it found an error.
            (shortcut for ErrorManager.ErrorMode)
            </summary>
        </member>
        <member name="E:FileHelpers.IFileHelperEngine`1.Progress">
            <summary>Called to notify progress.</summary>
        </member>
        <member name="T:FileHelpers.Events.INotifyRead">
            <summary>
            Interface used to provide In record notification of read operations.
            </summary>
            <remarks>INotifyRead and INotifyWrite will only be executed from engines that derive from <see cref="T:FileHelpers.EventEngineBase`1" />.</remarks>
            <example>
            <code>
            private class SampleType: INotifyRead, INotifyWrite
            { ....
            
            	public void AfterRead(AfterReadEventArgs e)
            	{
            		// Your Code Here
            	}
            	public void BeforeWrite(BeforeReadEventArgs e)
            	{
            		// Your Code Here
            	}
            
            }
            </code>
            </example>
        </member>
        <member name="M:FileHelpers.Events.INotifyRead.BeforeRead(FileHelpers.Events.BeforeReadEventArgs)">
            <summary>
            Method called by the engines before fill the info of the record and
            after read the source line.
            </summary>
            <param name="e">The Event Info</param>
        </member>
        <member name="M:FileHelpers.Events.INotifyRead.AfterRead(FileHelpers.Events.AfterReadEventArgs)">
            <summary>
            Method called by the engines after read a record from the source data.
            </summary>
            <param name="e">The Event Info</param>
        </member>
        <member name="T:FileHelpers.Events.INotifyWrite">
            <summary>
            Interface used to provide <b>In record notification of write operations.</b>
            </summary>
            <remarks>INotifyRead and INotifyWrite will only be executed from engines that derive from <see cref="T:FileHelpers.EventEngineBase`1" />.</remarks>
            <example>
            <code>
            private class SampleType: INotifyRead, INotifyWrite
            { ....
            
            	public void AfterRead(BeforeWriteEventArgs e)
            	{
            		// Your Code Here
            	}
            	public void BeforeWrite(AfterWriteEventArgs e)
            	{
            		// Your Code Here
            	}
            
            }
            </code>
            </example>
        </member>
        <member name="M:FileHelpers.Events.INotifyWrite.BeforeWrite(FileHelpers.Events.BeforeWriteEventArgs)">
            <summary>
            Method called by the engines before write a record to the
            destination stream.
            </summary>
            <param name="e">The Event Info</param>
        </member>
        <member name="M:FileHelpers.Events.INotifyWrite.AfterWrite(FileHelpers.Events.AfterWriteEventArgs)">
            <summary>
            Method called by the engines after write a record to the
            destination stream.
            </summary>
            <param name="e">The Event Info</param>
        </member>
        <member name="T:FileHelpers.Events.AfterReadEventArgs">
            <summary>Arguments for the <see cref="T:FileHelpers.Events.AfterReadHandler`1"/></summary>
        </member>
        <member name="M:FileHelpers.Events.AfterReadEventArgs.#ctor(FileHelpers.EngineBase,System.String,System.Boolean,System.Int32)">
            <summary>
            After the record is read,  allow details to be inspected.
            </summary>
            <param name="engine">Engine that parsed the record</param>
            <param name="line">Record that was analysed</param>
            <param name="lineChanged">Was it changed before</param>
            <param name="lineNumber">Record number read</param>
        </member>
        <member name="T:FileHelpers.Events.AfterReadEventArgs`1">
            <summary>Arguments for the <see cref="T:FileHelpers.Events.AfterReadHandler`1"/></summary>
        </member>
        <member name="M:FileHelpers.Events.AfterReadEventArgs`1.#ctor(FileHelpers.EventEngineBase{`0},System.String,System.Boolean,`0,System.Int32)">
            <summary>
            After the record is read,  allow details to be inspected.
            </summary>
            <param name="engine">Engine that parsed the record</param>
            <param name="line">Record that was analysed</param>
            <param name="lineChanged">Was it changed before</param>
            <param name="newRecord">Object created</param>
            <param name="lineNumber">Record number read</param>
        </member>
        <member name="P:FileHelpers.Events.AfterReadEventArgs`1.Record">
            <summary>The current record.</summary>
        </member>
        <member name="T:FileHelpers.Events.AfterWriteEventArgs">
            <summary>Arguments for the <see cref="T:FileHelpers.Events.AfterWriteEventArgs`1"/></summary>
        </member>
        <member name="M:FileHelpers.Events.AfterWriteEventArgs.#ctor(FileHelpers.EngineBase,System.Int32,System.String)">
            <summary>
            Record parsed after engine has finished
            </summary>
            <param name="engine">engine that created the record</param>
            <param name="lineNumber">Record number of the record</param>
            <param name="line">LIne to be written</param>
        </member>
        <member name="P:FileHelpers.Events.AfterWriteEventArgs.RecordLine">
            <summary>
            The line to be written to the destination.
            WARNING: you can change the line value and the engines will write it to the destination.
            </summary>
        </member>
        <member name="T:FileHelpers.Events.AfterWriteEventArgs`1">
            <summary>Arguments for the <see cref="T:FileHelpers.Events.AfterWriteEventArgs`1"/></summary>
        </member>
        <member name="M:FileHelpers.Events.AfterWriteEventArgs`1.#ctor(FileHelpers.EngineBase,`0,System.Int32,System.String)">
            <summary>
            Record parsed after engine has finished
            </summary>
            <param name="engine">engine that created the record</param>
            <param name="record">object created</param>
            <param name="lineNumber">Record number of the record</param>
            <param name="line">LIne to be written</param>
        </member>
        <member name="P:FileHelpers.Events.AfterWriteEventArgs`1.Record">
            <summary>The current record.</summary>
        </member>
        <member name="T:FileHelpers.Events.BeforeReadEventArgs">
            <summary>Arguments for the <see cref="T:FileHelpers.Events.BeforeReadHandler`1"/></summary>
        </member>
        <member name="M:FileHelpers.Events.BeforeReadEventArgs.#ctor(FileHelpers.EngineBase,System.String,System.Int32)">
            <summary>
            Record before being parsed by the engine
            </summary>
            <param name="engine">Engine that will analyse the record</param>
            <param name="line">Record read from the source</param>
            <param name="lineNumber">record number read</param>
        </member>
        <member name="T:FileHelpers.Events.BeforeReadEventArgs`1">
            <summary>Arguments for the <see cref="T:FileHelpers.Events.BeforeReadHandler`1"/></summary>
        </member>
        <member name="M:FileHelpers.Events.BeforeReadEventArgs`1.#ctor(FileHelpers.EngineBase,`0,System.String,System.Int32)">
            <summary>
            Record before being parsed by the engine
            </summary>
            <param name="engine">Engine that will analyse the record</param>
            <param name="record">Object to be created</param>
            <param name="line">Record read from the source</param>
            <param name="lineNumber">record number read</param>
        </member>
        <member name="P:FileHelpers.Events.BeforeReadEventArgs`1.Record">
            <summary>The current record that was just assigned not yet filled</summary>
        </member>
        <member name="T:FileHelpers.Events.BeforeWriteEventArgs">
            <summary>Arguments for the <see cref="T:FileHelpers.Events.BeforeWriteHandler`1"/></summary>
        </member>
        <member name="M:FileHelpers.Events.BeforeWriteEventArgs.#ctor(FileHelpers.EngineBase,System.Int32)">
            <summary>
            Check record just before processing.
            </summary>
            <param name="engine">Engine that will parse record</param>
            <param name="lineNumber">line number to be parsed</param>
        </member>
        <member name="P:FileHelpers.Events.BeforeWriteEventArgs.SkipThisRecord">
            <summary>Set this property as true if you want to bypass the current record.</summary>
        </member>
        <member name="T:FileHelpers.Events.BeforeWriteEventArgs`1">
            <summary>Arguments for the <see cref="T:FileHelpers.Events.BeforeWriteHandler`1"/></summary>
            <typeparam name="T">Object type we are writing from</typeparam>
        </member>
        <member name="M:FileHelpers.Events.BeforeWriteEventArgs`1.#ctor(FileHelpers.EngineBase,`0,System.Int32)">
            <summary>
            Check record just before processing.
            </summary>
            <param name="engine">Engine that will parse record</param>
            <param name="record">object to be created</param>
            <param name="lineNumber">line number to be parsed</param>
        </member>
        <member name="P:FileHelpers.Events.BeforeWriteEventArgs`1.Record">
            <summary>The current record.</summary>
        </member>
        <member name="T:FileHelpers.Events.BeforeReadHandler`1">
            <summary>
            Called in read operations just before the record string is translated to a record.
            </summary>
            <param name="engine">The engine that generates the event.</param>
            <param name="e">The event data.</param>
        </member>
        <member name="T:FileHelpers.Events.AfterReadHandler`1">
            <summary>
            Called in read operations just after the record was created from a record string.
            </summary>
            <param name="engine">The engine that generates the event.</param>
            <param name="e">The event data.</param>
        </member>
        <member name="T:FileHelpers.Events.BeforeWriteHandler`1">
            <summary>
            Called in write operations just before the record is converted to a string to write it.
            </summary>
            <param name="engine">The engine that generates the event.</param>
            <param name="e">The event data.</param>
        </member>
        <member name="T:FileHelpers.Events.AfterWriteHandler`1">
            <summary>
            Called in write operations just after the record was converted to a string.
            </summary>
            <param name="engine">The engine that generates the event.</param>
            <param name="e">The event data.</param>
        </member>
        <member name="T:FileHelpers.Events.FileHelpersEventArgs">
            <summary>
            Event args to signal engine failures
            </summary>
        </member>
        <member name="M:FileHelpers.Events.FileHelpersEventArgs.#ctor(FileHelpers.EngineBase,System.Int32)">
            <summary>
            Define an event message for an engine
            </summary>
            <param name="engine">Engine type</param>
            <param name="lineNumber">Line number error occurred</param>
        </member>
        <member name="P:FileHelpers.Events.FileHelpersEventArgs.Engine">
            <summary> The engine that raise the event </summary>
        </member>
        <member name="P:FileHelpers.Events.FileHelpersEventArgs.LineNumber">
            <summary>The current line number.</summary>
        </member>
        <member name="T:FileHelpers.Events.ProgressEventArgs">
            <summary>Class used to notify the current progress position and other context info.</summary>
        </member>
        <member name="P:FileHelpers.Events.ProgressEventArgs.Percent">
            <summary>
            Percentage of the file complete (estimate or completion time)
            </summary>
        </member>
        <member name="P:FileHelpers.Events.ProgressEventArgs.CurrentRecord">
            <summary>
            Number of the record being processed
            </summary>
        </member>
        <member name="P:FileHelpers.Events.ProgressEventArgs.TotalRecords">
            <summary>
            Total records in the file  (-1 is unknown)
            </summary>
        </member>
        <member name="P:FileHelpers.Events.ProgressEventArgs.CurrentBytes">
            <summary>
            Current position in the file
            </summary>
        </member>
        <member name="P:FileHelpers.Events.ProgressEventArgs.TotalBytes">
            <summary>
            Total bytes in the file
            </summary>
        </member>
        <member name="M:FileHelpers.Events.ProgressEventArgs.#ctor(System.Int32,System.Int32)">
            <summary>
            Create a progress event argument
            </summary>
            <param name="currentRecord">Current record in file</param>
            <param name="totalRecords">Total records in file</param>
        </member>
        <member name="M:FileHelpers.Events.ProgressEventArgs.#ctor(System.Int32,System.Int32,System.Int64,System.Int64)">
            <summary>
            Create a progress event argument
            </summary>
            <param name="currentRecord">Current record in file</param>
            <param name="totalRecords">Total number of records in file</param>
            <param name="currentBytes">Current position in bytes</param>
            <param name="totalBytes">Total bytes in file</param>
        </member>
        <member name="T:FileHelpers.Events.ReadEventArgs">
            <summary>
            Base class of 
            <see cref="T:FileHelpers.Events.BeforeReadEventArgs"/>
            and 
            <see cref="T:FileHelpers.Events.AfterReadEventArgs"/>
            </summary>
        </member>
        <member name="M:FileHelpers.Events.ReadEventArgs.#ctor(FileHelpers.EngineBase,System.String,System.Int32)">
            <summary>
            Create a read event argument, contains line number and record read
            </summary>
            <param name="engine">Engine used to parse data</param>
            <param name="line">record to be analysed</param>
            <param name="lineNumber">record count read</param>
        </member>
        <member name="P:FileHelpers.Events.ReadEventArgs.RecordLine">
            <summary>The record line just read.</summary>
        </member>
        <member name="P:FileHelpers.Events.ReadEventArgs.RecordLineChanged">
            <summary>Whether the RecordLine property has been written-to.</summary>
        </member>
        <member name="P:FileHelpers.Events.ReadEventArgs.SkipThisRecord">
            <summary>Set this property to true if you want to bypass the current record.</summary>
        </member>
        <member name="T:FileHelpers.Events.WriteEventArgs">
            <summary>Base class of <see cref="T:FileHelpers.Events.BeforeWriteEventArgs`1"/> and <see cref="T:FileHelpers.Events.AfterWriteEventArgs`1"/></summary>
        </member>
        <member name="M:FileHelpers.Events.WriteEventArgs.#ctor(FileHelpers.EngineBase,System.Int32)">
            <summary>
            Write events are based on this
            </summary>
            <param name="engine">Engine parsing data</param>
            <param name="lineNumber">Record number</param>
        </member>
        <member name="M:FileHelpers.Engines.StreamHelper.CreateFileAppender(System.String,System.Text.Encoding,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            open a stream with optional trim extra blank lines
            </summary>
            <param name="fileName">Filename to open</param>
            <param name="encode">encoding of the file</param>
            <param name="correctEnd">do we trim blank lines from end?</param>
            <param name="disposeStream">do we close stream after trimming</param>
            <param name="bufferSize">Buffer size to read</param>
            <returns>TextWriter ready to write to</returns>
        </member>
        <member name="T:FileHelpers.BadUsageException">
            <summary>Indicates the wrong usage of the library.</summary>
        </member>
        <member name="M:FileHelpers.BadUsageException.#ctor(System.String)">
            <summary>Creates an instance of an BadUsageException.</summary>
            <param name="message">The exception Message</param>
        </member>
        <member name="M:FileHelpers.BadUsageException.#ctor(System.Int32,System.Int32,System.String)">
            <summary>Creates an instance of an BadUsageException.</summary>
            <param name="message">The exception Message</param>
            <param name="line">The line number where the problem was found</param>
            <param name="column">The column number where the problem was found</param>
        </member>
        <member name="M:FileHelpers.BadUsageException.#ctor(FileHelpers.LineInfo,System.String)">
            <summary>Creates an instance of an BadUsageException.</summary>
            <param name="message">The exception Message</param>
            <param name="line">Line to display in message</param>
        </member>
        <member name="T:FileHelpers.ConvertException">
            <summary>
            Indicates that a string value can't be converted to a dest type.
            </summary>
        </member>
        <member name="P:FileHelpers.ConvertException.FieldType">
            <summary>The destination type.</summary>
        </member>
        <member name="P:FileHelpers.ConvertException.FieldStringValue">
            <summary>The value that can't be converted. (null for unknown)</summary>
        </member>
        <member name="P:FileHelpers.ConvertException.MessageExtra">
            <summary>Extra info about the error.</summary>
        </member>
        <member name="P:FileHelpers.ConvertException.MessageOriginal">
            <summary>The message without the Line, Column and FieldName.</summary>
        </member>
        <member name="P:FileHelpers.ConvertException.FieldName">
            <summary>The name of the field related to the exception. (null for unknown)</summary>
        </member>
        <member name="P:FileHelpers.ConvertException.LineNumber">
            <summary>The line where the error was found. (-1 is unknown)</summary>
        </member>
        <member name="P:FileHelpers.ConvertException.ColumnNumber">
            <summary>The estimate column where the error was found. (-1 is unknown)</summary>
        </member>
        <member name="M:FileHelpers.ConvertException.#ctor(System.String,System.Type)">
            <summary>
            Create a new ConvertException object
            </summary>
            <param name="origValue">The value to convert.</param>
            <param name="destType">The destination Type.</param>
        </member>
        <member name="M:FileHelpers.ConvertException.#ctor(System.String,System.Type,System.String)">
            <summary>
            Create a new ConvertException object
            </summary>
            <param name="origValue">The value to convert.</param>
            <param name="destType">The destination Type.</param>
            <param name="extraInfo">Additional info of the error.</param>
        </member>
        <member name="M:FileHelpers.ConvertException.#ctor(System.String,System.Type,System.String,System.Int32,System.Int32,System.String,System.Exception)">
            <summary>
            Create a new ConvertException object
            </summary>
            <param name="origValue">The value to convert.</param>
            <param name="destType">The destination Type.</param>
            <param name="extraInfo">Additional info of the error.</param>
            <param name="columnNumber">The estimated column number.</param>
            <param name="lineNumber">The line where the error was found.</param>
            <param name="fieldName">The name of the field with the error</param>
            <param name="innerEx">The Inner Exception</param>
        </member>
        <member name="T:FileHelpers.ErrorInfo">
            <summary>
            Contains error information of the <see cref="T:FileHelpers.FileHelperEngine"/> class.
            </summary>
        </member>
        <member name="M:FileHelpers.ErrorInfo.#ctor">
            <summary>
            Contains error information of the <see cref="T:FileHelpers.FileHelperEngine"/> class.
            </summary>
        </member>
        <member name="F:FileHelpers.ErrorInfo.mLineNumber">
            <summary>
            Line number of the error
            </summary>
        </member>
        <member name="P:FileHelpers.ErrorInfo.LineNumber">
            <summary>The line number of the error</summary>
        </member>
        <member name="F:FileHelpers.ErrorInfo.mRecordString">
            <summary>The string of the record of the error.</summary>
        </member>
        <member name="P:FileHelpers.ErrorInfo.RecordString">
            <summary>The string of the record of the error.</summary>
        </member>
        <member name="F:FileHelpers.ErrorInfo.mRecordTypeName">
            <summary>The name of the record type being used.</summary>
        </member>
        <member name="P:FileHelpers.ErrorInfo.RecordTypeName">
            <summary>>The name of the record type being used.</summary>
        </member>
        <member name="F:FileHelpers.ErrorInfo.mExceptionInfo">
            <summary>The exception that indicates the error.</summary>
        </member>
        <member name="P:FileHelpers.ErrorInfo.ExceptionInfo">
            <summary>The exception that indicates the error.</summary>
        </member>
        <member name="T:FileHelpers.ErrorInfo.ExceptionConverter">
            <summary>
            Converter exception
            </summary>
        </member>
        <member name="M:FileHelpers.ErrorInfo.ExceptionConverter.FieldToString(System.Object)">
            <summary>
            Convert a field definition to a string
            </summary>
            <param name="from">Convert exception object</param>
            <returns>Field as a string or null</returns>
        </member>
        <member name="M:FileHelpers.ErrorInfo.ExceptionConverter.StringToField(System.String)">
            <summary>
            Convert a general exception to a string
            </summary>
            <param name="from">exception to convert</param>
            <returns>Exception from field</returns>
        </member>
        <member name="T:FileHelpers.ErrorManager">
            <summary>
            This is the class that handles the errors of the engines process.
            </summary>
            <remarks>
            All the engines and DataStorage utilities contains an ErrorManager.
            </remarks>
        </member>
        <member name="M:FileHelpers.ErrorManager.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:FileHelpers.ErrorManager"/> class.
            </summary>
        </member>
        <member name="M:FileHelpers.ErrorManager.#ctor(FileHelpers.ErrorMode)">
            <summary>
            Initializes a new instance of the <see cref="T:FileHelpers.ErrorManager"/> class.
            with the specified <see cref="P:FileHelpers.ErrorManager.ErrorMode"/>.
            </summary>
            <param name="mode">Indicates the error behavior of the class.</param>
        </member>
        <member name="P:FileHelpers.ErrorManager.ErrorLimit">
            <summary>Maximum number of recorded errors. After this limit is reached, successive errors are ignored.</summary>
            <remarks>Default error limit is 10000.</remarks>
        </member>
        <member name="P:FileHelpers.ErrorManager.Errors">
            <summary>
            Is an array of <see cref="T:FileHelpers.ErrorInfo"/> that contains the
            errors of the last operation in this class.
            </summary>
        </member>
        <member name="P:FileHelpers.ErrorManager.ErrorMode">
            <summary>
            Indicates the behavior of the <see cref="T:FileHelpers.FileHelperEngine"/>
            when it found an error.
            </summary>
            <remarks>Default error mode is ThrowException.</remarks>
        </member>
        <member name="P:FileHelpers.ErrorManager.ErrorCount">
            <summary>Number of contained errors.</summary>
        </member>
        <member name="P:FileHelpers.ErrorManager.HasErrors">
            <summary>Indicates if contains one or more errors.</summary>
        </member>
        <member name="M:FileHelpers.ErrorManager.ClearErrors">
            <summary>Clears the error collection.</summary>
        </member>
        <member name="M:FileHelpers.ErrorManager.AddError(FileHelpers.ErrorInfo)">
            <summary>Add the specified ErrorInfo to the contained collection.</summary>
            <param name="error"></param>
        </member>
        <member name="M:FileHelpers.ErrorManager.AddErrors(FileHelpers.ErrorManager)">
            <summary>Add the specified ErrorInfo to the contained collection.</summary>
        </member>
        <member name="M:FileHelpers.ErrorManager.SaveErrors(System.String)">
            <summary>Saves the contained errors to the specified file.</summary>
            <param name="fileName">The file that contains the errors.</param>
        </member>
        <member name="M:FileHelpers.ErrorManager.SaveErrors(System.String,System.String)">
            <summary>Saves the contained errors to the specified file.</summary>
            <param name="fileName">The file that contains the errors.</param>
            <param name="header">The header line of the errors file.</param>
        </member>
        <member name="M:FileHelpers.ErrorManager.LoadErrors(System.String)">
            <summary>Load errors from a file.</summary>
            <param name="fileName">The file that contains the errors.</param>
        </member>
        <member name="M:FileHelpers.ErrorManager.GetEnumerator">
            <summary>
             Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
             An <see cref="T:System.Collections.IEnumerator"></see>
             object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="T:FileHelpers.ErrorMode">
            <summary>Indicates the behavior when the engine classes like <see cref="T:FileHelpers.FileHelperEngine"/> class found an error.</summary>
        </member>
        <member name="F:FileHelpers.ErrorMode.ThrowException">
            <summary>Default value, this simply Rethrow the original exception.</summary>
        </member>
        <member name="F:FileHelpers.ErrorMode.SaveAndContinue">
            <summary>Add an <see cref="T:FileHelpers.ErrorInfo"/> to the array of <see cref="P:FileHelpers.ErrorManager.Errors"/>.</summary>
        </member>
        <member name="F:FileHelpers.ErrorMode.IgnoreAndContinue">
            <summary>Simply ignores the exception and continues</summary>
        </member>
        <member name="T:FileHelpers.FileHelpersException">
            <summary>Base class for all the library Exceptions.</summary>
        </member>
        <member name="M:FileHelpers.FileHelpersException.#ctor(System.String)">
            <summary>Basic constructor of the exception.</summary>
            <param name="message">Message of the exception.</param>
        </member>
        <member name="M:FileHelpers.FileHelpersException.#ctor(System.String,System.Exception)">
            <summary>Basic constructor of the exception.</summary>
            <param name="message">Message of the exception.</param>
            <param name="innerEx">The inner Exception.</param>
        </member>
        <member name="M:FileHelpers.FileHelpersException.#ctor(System.Int32,System.Int32,System.String)">
            <summary>Basic constructor of the exception.</summary>
            <param name="message">Message of the exception.</param>
            <param name="line">The line number where the problem was found</param>
            <param name="column">The column number where the problem was found</param>
        </member>
        <member name="T:FileHelpers.NullValueNotFoundException">
            <summary>Indicates the wrong usage of the library.</summary>
        </member>
        <member name="M:FileHelpers.NullValueNotFoundException.#ctor(System.String)">
            <summary>Creates an instance of an NullValueNotFoundException.</summary>
            <param name="message">The exception Message</param>
        </member>
        <member name="M:FileHelpers.NullValueNotFoundException.#ctor(System.Int32,System.Int32,System.String)">
            <summary>Creates an instance of an NullValueNotFoundException.</summary>
            <param name="message">The exception Message</param>
            <param name="line">The line number where the problem was found</param>
            <param name="column">The column number where the problem was found</param>
        </member>
        <member name="M:FileHelpers.NullValueNotFoundException.#ctor(FileHelpers.LineInfo,System.String)">
            <summary>Creates an instance of an NullValueNotFoundException.</summary>
            <param name="message">The exception Message</param>
            <param name="line">Line to display in message</param>
        </member>
        <member name="T:FileHelpers.AlignMode">
            <summary>Indicates the align of the field when the <see cref="T:FileHelpers.FileHelperEngine"/> <b>writes</b> the record.</summary>
        </member>
        <member name="F:FileHelpers.AlignMode.Left">
            <summary>Aligns the field to the left.</summary>
        </member>
        <member name="F:FileHelpers.AlignMode.Center">
            <summary>Aligns the field to the center.</summary>
        </member>
        <member name="F:FileHelpers.AlignMode.Right">
            <summary>Aligns the field to the right.</summary>
        </member>
        <member name="T:FileHelpers.DelimitedField">
            <summary>
            Define a field that is delimited, eg CSV and may be quoted
            </summary>
        </member>
        <member name="M:FileHelpers.DelimitedField.#ctor">
            <summary>
            Create an empty delimited field structure
            </summary>
        </member>
        <member name="M:FileHelpers.DelimitedField.#ctor(System.Reflection.FieldInfo,System.String,System.String)">
            <summary>
            Create a delimited field with defined separator
            </summary>
            <param name="fi">field info structure</param>
            <param name="sep">field separator</param>
            <param name="defaultCultureName">Default culture name used for each properties if no converter is specified otherwise. If null, the default decimal separator (".") will be used.</param>
        </member>
        <member name="P:FileHelpers.DelimitedField.Separator">
            <summary>
            Set the separator string
            </summary>
            <remarks>Also sets the discard count</remarks>
        </member>
        <member name="P:FileHelpers.DelimitedField.QuoteMultiline">
            <summary>
            allow a quoted multiline format
            </summary>
        </member>
        <member name="P:FileHelpers.DelimitedField.QuoteMode">
            <summary>
            whether quotes are optional for read and / or write
            </summary>
        </member>
        <member name="P:FileHelpers.DelimitedField.QuoteChar">
            <summary>
            quote character around field (and repeated within it)
            </summary>
        </member>
        <member name="M:FileHelpers.DelimitedField.ExtractFieldString(FileHelpers.LineInfo)">
            <summary>
            Extract the field from the delimited file, removing separators and quotes
            and any duplicate quotes within the record
            </summary>
            <param name="line">line containing record input</param>
            <returns>Extract information</returns>
        </member>
        <member name="M:FileHelpers.DelimitedField.ExtractQuotedString(FileHelpers.LineInfo,System.Char,System.Boolean)">
            <summary>
            Extract a string from a quoted string, allows for doubling the quotes
            for example 'o''clock'
            </summary>
            <param name="line">Line to extract from (with extra info)</param>
            <param name="quoteChar">Quote char to remove</param>
            <param name="allowMultiline">can we have a new line in middle of string</param>
            <returns>Extracted information</returns>
        </member>
        <member name="M:FileHelpers.DelimitedField.CreateFieldString(System.Text.StringBuilder,System.String,System.Boolean)">
            <summary>
            Output the field string adding delimiters and any required quotes
            </summary>
            <param name="sb">buffer to add field to</param>
            <param name="field">value to add</param>
            <param name="isLast">Indicates if we are processing last field</param>
        </member>
        <member name="M:FileHelpers.DelimitedField.CreateQuotedString(System.Text.StringBuilder,System.String,System.Char)">
            <summary>
            Convert a string to a string with quotes around it,
            if the quote appears within the string it is doubled
            </summary>
            <param name="sb">Where string is added</param>
            <param name="source">String to be added</param>
            <param name="quoteChar">quote character to use, eg "</param>
        </member>
        <member name="M:FileHelpers.DelimitedField.CreateClone">
            <summary>
            create a field base class and populate the delimited values
            base class will add its own values
            </summary>
            <returns>fieldbase ready to be populated with extra info</returns>
        </member>
        <member name="T:FileHelpers.FieldBase">
            <summary>
            Base class for all Field Types.
            Implements all the basic functionality of a field in a typed file.
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.FieldType">
            <summary>
            type of object to be created,  eg DateTime
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.Converter">
            <summary>
            Provider to convert to and from text
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.CharsToDiscard">
            <summary>
            Number of extra characters used,  delimiters and quote characters
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.FieldTypeInternal">
            <summary>
            Field type of an array or it is just fieldType.
            What actual object will be created
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.IsArray">
            <summary>
            Is this field an array?
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.ArrayMinLength">
            <summary>
            Array must have this many entries
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.ArrayMaxLength">
            <summary>
            Array may have this many entries,  if equal to ArrayMinLength then
            it is a fixed length array
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.ArrayType">
            <summary>
            Seems to be duplicate of FieldTypeInternal except it is ONLY set
            for an array
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.Discarded">
            <summary>
            Do we process this field but not store the value
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.NullValue">
            <summary>
            Value to use if input is null or empty
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.IsStringField">
            <summary>
            Are we a simple string field we can just assign to
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.FieldInfo">
            <summary>
            Details about the extraction criteria
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.TrimMode">
            <summary>
            indicates whether we trim leading and/or trailing whitespace
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.TrimChars">
            <summary>
            Character to chop off front and / rear of the string
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.IsOptional">
            <summary>
            The field may not be present on the input data (line not long enough)
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.NextIsOptional">
            <summary>
            The next field along is optional,  optimise processing next records
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.IsFirst">
            <summary>
            Am I the first field in an array list
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.IsLast">
            <summary>
            Am I the last field in the array list
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.InNewLine">
            <summary>
            Set from the FieldInNewLIneAtribute.  This field begins on a new
            line of the file
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.FieldOrder">
            <summary>
            Order of the field in the file layout
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.IsNullableType">
            <summary>
            Can null be assigned to this value type, for example not int or
            DateTime
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.FieldFriendlyName">
            <summary>
            Name of the field without extra characters (eg property)
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.IsNotEmpty">
            <summary>
            The field must be not be empty
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.FieldCaption">
            <summary>
            Caption of the field displayed in header row (see EngineBase.GetFileHeader)
            </summary>
        </member>
        <member name="P:FileHelpers.FieldBase.FieldName">
            <summary>
            Fieldname of the field we are storing
            </summary>
        </member>
        <member name="M:FileHelpers.FieldBase.CreateField(System.Reflection.FieldInfo,FileHelpers.TypedRecordAttribute)">
            <summary>
            Check the Attributes on the field and return a structure containing
            the settings for this file.
            </summary>
            <param name="fi">Information about this field</param>
            <param name="recordAttribute">Type of record we are reading</param>
            <returns>Null if not used</returns>
        </member>
        <member name="M:FileHelpers.FieldBase.#ctor">
            <summary>
            Create a field base without any configuration
            </summary>
        </member>
        <member name="M:FileHelpers.FieldBase.#ctor(System.Reflection.FieldInfo,System.String)">
            <summary>
            Create a field base from a fieldinfo object
            Verify the settings against the actual field to ensure it will work.
            </summary>
            <param name="fi">Field Info Object</param>
            <param name="defaultCultureName">Default culture name used for each properties if no converter is specified otherwise. If null, the default decimal separator (".") will be used.</param>
        </member>
        <member name="M:FileHelpers.FieldBase.ExtractFieldString(FileHelpers.LineInfo)">
            <summary>
            Extract the string from the underlying data, removes quotes
            characters for example
            </summary>
            <param name="line">Line to parse data from</param>
            <returns>Slightly processed string from the data</returns>
        </member>
        <member name="M:FileHelpers.FieldBase.CreateFieldString(System.Text.StringBuilder,System.String,System.Boolean)">
            <summary>
            Create a text block containing the field from definition
            </summary>
            <param name="sb">Append string to output</param>
            <param name="field">Field we are adding</param>
            <param name="isLast">Indicates if we are processing last field</param>
        </member>
        <member name="M:FileHelpers.FieldBase.ExtractFieldValue(FileHelpers.LineInfo)">
            <summary>
            Get the data out of the records
            </summary>
            <param name="line">Line handler containing text</param>
            <returns></returns>
        </member>
        <member name="M:FileHelpers.FieldBase.AssignFromString(FileHelpers.ExtractedInfo,FileHelpers.LineInfo)">
            <summary>
            Create field object after extracting the string from the underlying
            input data
            </summary>
            <param name="fieldString">Information extracted?</param>
            <param name="line">Underlying input data</param>
            <returns>Object to assign to field</returns>
        </member>
        <member name="M:FileHelpers.FieldBase.GetNullValue(FileHelpers.LineInfo)">
            <summary>
            Convert a null value into a representation,
            allows for a null value override
            </summary>
            <param name="line">input line to read, used for error messages</param>
            <returns>Null value for object</returns>
        </member>
        <member name="M:FileHelpers.FieldBase.GetDiscardedNullValue">
            <summary>
            Get the null value that represent a discarded value
            </summary>
            <returns>null value of discard?</returns>
        </member>
        <member name="M:FileHelpers.FieldBase.CreateValueForField(System.Object)">
            <summary>
            Convert a field value into a write able value
            </summary>
            <param name="fieldValue">object value to convert</param>
            <returns>converted value</returns>
        </member>
        <member name="M:FileHelpers.FieldBase.AssignToString(System.Text.StringBuilder,System.Object)">
            <summary>
            convert field to string value and assign to a string builder
            buffer for output
            </summary>
            <param name="sb">buffer to collect record</param>
            <param name="fieldValue">value to convert</param>
        </member>
        <member name="M:FileHelpers.FieldBase.Clone">
            <summary>
            Copy the field object
            </summary>
            <returns>a complete copy of the Field object</returns>
        </member>
        <member name="M:FileHelpers.FieldBase.CreateClone">
            <summary>
            Add the extra details that derived classes create
            </summary>
            <returns>field clone of right type</returns>
        </member>
        <member name="T:FileHelpers.FixedLengthField">
            <summary>
            Fixed length field that has length and alignment
            </summary>
        </member>
        <member name="P:FileHelpers.FixedLengthField.FieldLength">
            <summary>
            Field length of this field in the record
            </summary>
        </member>
        <member name="P:FileHelpers.FixedLengthField.Align">
            <summary>
            Alignment of this record
            </summary>
        </member>
        <member name="P:FileHelpers.FixedLengthField.FixedMode">
            <summary>
            Whether we allow more or less characters to be handled
            </summary>
        </member>
        <member name="P:FileHelpers.FixedLengthField.OverflowMode">
            <summary>
            How an overflowing field value is handled
            </summary>
        </member>
        <member name="M:FileHelpers.FixedLengthField.#ctor">
            <summary>
            Simple fixed length field constructor
            </summary>
        </member>
        <member name="M:FileHelpers.FixedLengthField.#ctor(System.Reflection.FieldInfo,System.Int32,FileHelpers.Enums.OverflowMode,FileHelpers.FieldAlignAttribute,System.String)">
            <summary>
            Create a fixed length field from field information
            </summary>
            <param name="fi">Field definitions</param>
            <param name="length">Length of this field</param>
            <param name="overflowMode">Overflow mode of this field</param>
            <param name="align">Alignment, left or right</param>
            <param name="defaultCultureName">Default culture name used for each properties if no converter is specified otherwise. If null, the default decimal separator (".") will be used.</param>
        </member>
        <member name="M:FileHelpers.FixedLengthField.ExtractFieldString(FileHelpers.LineInfo)">
            <summary>
            Get the value from the record
            </summary>
            <param name="line">line to extract from</param>
            <returns>Information extracted from record</returns>
        </member>
        <member name="M:FileHelpers.FixedLengthField.CreateFieldString(System.Text.StringBuilder,System.String,System.Boolean)">
            <summary>
            Create a fixed length string representation (pad it out or truncate it)
            </summary>
            <param name="sb">buffer to add field to</param>
            <param name="field">value we are updating with</param>
            <param name="isLast">Indicates if we are processing last field</param>
        </member>
        <member name="M:FileHelpers.FixedLengthField.CreateClone">
            <summary>
            Create a clone of the fixed length record ready to get updated by
            the base settings
            </summary>
            <returns>new fixed length field definition just like this one minus
            the base settings</returns>
        </member>
        <member name="T:FileHelpers.FixedMode">
            <summary>
            Indicates the behavior when variable length records are found in a
            [<see cref="T:FileHelpers.FixedLengthRecordAttribute"/>]. (Note: nothing in
            common with [FieldOptional])
            </summary>
        </member>
        <member name="F:FileHelpers.FixedMode.ExactLength">
            <summary>
            The records must have the length equals to the sum of each
            field length. Default Behavior.
            </summary>
        </member>
        <member name="F:FileHelpers.FixedMode.AllowMoreChars">
            <summary>
            The records can contain more chars after the last field
            </summary>
        </member>
        <member name="F:FileHelpers.FixedMode.AllowLessChars">
            <summary>
            The records can contain less chars. Based on the
            combination with FieldOptional the records can contain less
            fields in the last, or if it is marked as optional, in the
            previous field.
            </summary>
        </member>
        <member name="F:FileHelpers.FixedMode.AllowVariableLength">
            <summary>
            The records can contain more or less chars in the last
            field
            </summary>
        </member>
        <member name="T:FileHelpers.MultilineMode">
            <summary>Indicates the behavior of multiline fields.</summary>
        </member>
        <member name="F:FileHelpers.MultilineMode.AllowForBoth">
            <summary>The engine can handle multiline values for read or write.</summary>
        </member>
        <member name="F:FileHelpers.MultilineMode.AllowForRead">
            <summary>The engine can handle multiline values only for read.</summary>
        </member>
        <member name="F:FileHelpers.MultilineMode.AllowForWrite">
            <summary>The engine can handle multiline values only for write.</summary>
        </member>
        <member name="F:FileHelpers.MultilineMode.NotAllow">
            <summary>The engine don't allow multiline values for this field.</summary>
        </member>
        <member name="T:FileHelpers.Enums.OverflowMode">
            <summary>
            Indicates the behavior when a data longer than a [<see cref="T:FileHelpers.FixedLengthRecordAttribute"/>]
            Length is written to this field
            </summary>
        </member>
        <member name="F:FileHelpers.Enums.OverflowMode.DiscardEnd">
            <summary>
            Discard overflowing characters at the end
            </summary>
        </member>
        <member name="F:FileHelpers.Enums.OverflowMode.Error">
            <summary>
            Throw an exception
            </summary>
        </member>
        <member name="T:FileHelpers.QuoteMode">
            <summary>Indicates the behavior of quoted fields.</summary>
        </member>
        <member name="F:FileHelpers.QuoteMode.AlwaysQuoted">
            <summary>
            The engines expects that the field must always be surrounded with
            quotes when reading and always adds the quotes when writing.
            </summary>
        </member>
        <member name="F:FileHelpers.QuoteMode.OptionalForRead">
            <summary>
            The engine can handle a field even if it is not surrounded with
            quotes while reading but it always add the quotes when writing.
            </summary>
        </member>
        <member name="F:FileHelpers.QuoteMode.OptionalForWrite">
            <summary>
            The engine always expects a quote when read and it will only add
            the quotes when writing only if the field contains quotes, new
            lines or the separator char.
            </summary>
        </member>
        <member name="F:FileHelpers.QuoteMode.OptionalForBoth">
            <summary>
            The engine can handle a field even if it is not surrounded with
            quotes while reading and it will only add the quotes when writing
            if the field contains quotes, new lines or the separator char.
            </summary>
        </member>
        <member name="T:FileHelpers.TrimMode">
            <summary>
            Indicates the trimming behavior of the leading and trailing whitespace.
            </summary>
        </member>
        <member name="F:FileHelpers.TrimMode.None">
            <summary>No trimming is performed.</summary>
        </member>
        <member name="F:FileHelpers.TrimMode.Both">
            <summary>The field is trimmed on both sides.</summary>
        </member>
        <member name="F:FileHelpers.TrimMode.Left">
            <summary>The field is trimmed on the left.</summary>
        </member>
        <member name="F:FileHelpers.TrimMode.Right">
            <summary>The field is trimmed on the right.</summary>
        </member>
        <member name="T:FileHelpers.Fields.TypeHelper">
            <summary>
            extensions to help with types
            </summary>
        </member>
        <member name="M:FileHelpers.Fields.TypeHelper.IsNumericType(System.Type)">
            <summary>
            Is this type any sort of numeric
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="T:FileHelpers.FileDiffEngine`1">
            <summary>
            Engine used to create diff files based on the
            <see cref="T:System.IComparable`1"/> interface.
            </summary>
            <typeparam name="T">The record type.</typeparam>
        </member>
        <member name="M:FileHelpers.FileDiffEngine`1.#ctor">
            <summary>
            Creates a new <see cref="T:FileHelpers.FileDiffEngine`1"/>
            </summary>
        </member>
        <member name="M:FileHelpers.FileDiffEngine`1.OnlyNewRecords(System.String,System.String)">
            <summary>Returns the records in newFile that not are in the sourceFile</summary>
            <param name="sourceFile">The file with the old records.</param>
            <param name="newFile">The file with the new records.</param>
            <returns>The records in newFile that not are in the sourceFile</returns>
        </member>
        <member name="M:FileHelpers.FileDiffEngine`1.OnlyMissingRecords(System.String,System.String)">
            <summary>Returns the records in newFile that not are in the sourceFile</summary>
            <param name="sourceFile">The file with the old records.</param>
            <param name="newFile">The file with the new records.</param>
            <returns>The records in newFile that not are in the sourceFile</returns>
        </member>
        <member name="M:FileHelpers.FileDiffEngine`1.OnlyDuplicatedRecords(System.String,System.String)">
            <summary>
            Returns the duplicated records in both files.
            </summary>
            <param name="file1">A file with records.</param>
            <param name="file2">A file with records.</param>
            <returns>The records that appear in both files.</returns>
        </member>
        <member name="M:FileHelpers.FileDiffEngine`1.OnlyNoDuplicatedRecords(System.String,System.String)">
            <summary>
            Returns the NON duplicated records in both files.
            </summary>
            <param name="file1">A file with record.</param>
            <param name="file2">A file with record.</param>
            <returns>The records that NOT appear in both files.</returns>
        </member>
        <member name="M:FileHelpers.FileDiffEngine`1.WriteNewRecords(System.String,System.String,System.String)">
            <summary>
            Read the source file, the new File, get the records and write
            them to a destination file.  (record not in first file but in
            second will be written to the third)
            </summary>
            <param name="sourceFile">The file with the source records.</param>
            <param name="newFile">The file with the new records.</param>
            <param name="destFile">The destination file.</param>
            <returns>The new records on the new file.</returns>
        </member>
        <member name="T:FileHelpers.FileTransformEngine.ExHelper">
            <summary>
            add validation exceptions
            </summary>
        </member>
        <member name="M:FileHelpers.FileTransformEngine.ExHelper.CheckNullParam(System.String,System.String)">
            <summary>
            Check that parameter is not null or empty and throw an exception
            </summary>
            <param name="param">value to check</param>
            <param name="paramName">parameter name</param>
        </member>
        <member name="M:FileHelpers.FileTransformEngine.ExHelper.CheckNullParam(System.Object,System.String)">
            <summary>
            Check that parameter is not null and throw an exception
            </summary>
            <param name="param">value to check</param>
            <param name="paramName">parameter name</param>
        </member>
        <member name="M:FileHelpers.FileTransformEngine.ExHelper.CheckDifferentsParams(System.Object,System.String,System.Object,System.String)">
            <summary>
            check that parameter 1 is different from parameter 2
            </summary>
            <param name="param1">value 1 to test</param>
            <param name="param1Name">name of value 1</param>
            <param name="param2">value 2 to test</param>
            <param name="param2Name">name of vlaue 2</param>
        </member>
        <member name="T:FileHelpers.FileTransformEngine`2">
            <summary>
            This class allow you to convert the records of a file to a different record format.
            </summary>
            <typeparam name="TSource">The source record type.</typeparam>
            <typeparam name="TDestination">The destination record type.</typeparam>
        </member>
        <member name="M:FileHelpers.FileTransformEngine`2.#ctor">
            <summary>Create a new FileTransformEngine.</summary>
        </member>
        <member name="P:FileHelpers.FileTransformEngine`2.ErrorMode">
            <summary>Indicates the behavior of the engine when an error is found.</summary>
        </member>
        <member name="P:FileHelpers.FileTransformEngine`2.SourceErrorManager">
            <summary>
            Allow access the <see cref="T:FileHelpers.ErrorManager"/> of the engine used to
            read the source file, is null before any file is transformed
            </summary>
        </member>
        <member name="P:FileHelpers.FileTransformEngine`2.DestinationErrorManager">
            <summary>
            Allow access the <see cref="T:FileHelpers.ErrorManager"/> of the engine used to
            write the destination file, is null before any file is transformed
            </summary>
        </member>
        <member name="M:FileHelpers.FileTransformEngine`2.TransformFile(System.String,System.String)">
            <summary>
            Transform the contents of the sourceFile and write them to the
            destFile.(use only if you need the array of the transformed
            records, TransformFileFast is faster)
            </summary>
            <param name="sourceFile">The source file.</param>
            <param name="destFile">The destination file.</param>
            <returns>The transformed records.</returns>
        </member>
        <member name="M:FileHelpers.FileTransformEngine`2.TransformFileFast(System.String,System.String)">
            <summary>
            Transform the contents of the sourceFile and write them to the
            destFile. (faster and uses less memory, best choice for big
            files)
            </summary>
            <param name="sourceFile">The source file.</param>
            <param name="destFile">The destination file.</param>
            <returns>The number of transformed records.</returns>
        </member>
        <member name="M:FileHelpers.FileTransformEngine`2.TransformFileFast(System.IO.TextReader,System.String)">
            <summary>
            Transform the contents of the sourceFile and write them to the
            destFile. (faster and uses less memory, best choice for big
            files)
            </summary>
            <param name="sourceStream">The source stream.</param>
            <param name="destFile">The destination file.</param>
            <returns>The number of transformed records.</returns>
        </member>
        <member name="M:FileHelpers.FileTransformEngine`2.TransformFileFast(System.IO.TextReader,System.IO.StreamWriter)">
            <summary>
            Transform the contents of the sourceFile and write them to the
            destFile. (faster and uses less memory, best choice for big
            files)
            </summary>
            <param name="sourceStream">The source stream.</param>
            <param name="destStream">The destination stream.</param>
            <returns>The number of transformed records.</returns>
        </member>
        <member name="M:FileHelpers.FileTransformEngine`2.TransformFileFast(System.String,System.IO.StreamWriter)">
            <summary>
            Transform the contents of the sourceFile and write them to the
            destFile. (faster and uses less memory, best choice for big
            files)
            </summary>
            <param name="sourceFile">The source file.</param>
            <param name="destStream">The destination stream.</param>
            <returns>The number of transformed records.</returns>
        </member>
        <member name="M:FileHelpers.FileTransformEngine`2.TransformRecords(`0[])">
            <summary>
            Transforms an array of records from the source type to the destination type
            </summary>
            <param name="sourceRecords">An array of the source records.</param>
            <returns>The transformed records.</returns>
        </member>
        <member name="M:FileHelpers.FileTransformEngine`2.ReadAndTransformRecords(System.String)">
            <summary>
            Transform a file that contains source records to an array of the destination type
            </summary>
            <param name="sourceFile">A file containing the source records.</param>
            <returns>The transformed records.</returns>
        </member>
        <member name="P:FileHelpers.FileTransformEngine`2.SourceType">
            <summary>The source record Type.</summary>
        </member>
        <member name="P:FileHelpers.FileTransformEngine`2.DestinationType">
            <summary>The destination record Type.</summary>
        </member>
        <member name="P:FileHelpers.FileTransformEngine`2.SourceEncoding">
            <summary>The Encoding of the Source File.</summary>
        </member>
        <member name="P:FileHelpers.FileTransformEngine`2.DestinationEncoding">
            <summary>The Encoding of the Destination File.</summary>
        </member>
        <member name="T:FileHelpers.ITransformable`1">
            <summary>
            Interface used to provide record type transformations
            </summary>
        </member>
        <member name="M:FileHelpers.ITransformable`1.TransformTo">
            <summary>
            Method called to transform the current record to Type T.
            </summary>
        </member>
        <member name="T:FileHelpers.MasterDetail.MasterDetailSelector">
            <summary>
            Delegate thats determines the Type of the current record (Master, Detail, Skip)
            </summary>
            <param name="recordString">The string of the current record.</param>
            <returns>the action used for the current record (Master, Detail, Skip)</returns>
        </member>
        <member name="T:FileHelpers.MasterDetail.CommonSelector">
            <summary>The Action taken when the selector string is found.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.CommonSelector.MasterIfContains">
            <summary>Parse the current record as <b>Master</b> if the selector string is found.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.CommonSelector.MasterIfBegins">
            <summary>Parse the current record as <b>Master</b> if the record starts with some string.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.CommonSelector.MasterIfEnds">
            <summary>Parse the current record as <b>Master</b> if the record ends with some string.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.CommonSelector.MasterIfEnclosed">
            <summary>Parse the current record as <b>Master</b> if the record begins and ends with some string.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.CommonSelector.DetailIfContains">
            <summary>Parse the current record as <b>Detail</b> if the selector string is found.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.CommonSelector.DetailIfBegins">
            <summary>Parse the current record as <b>Detail</b> if the record starts with some string.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.CommonSelector.DetailIfEnds">
            <summary>Parse the current record as <b>Detail</b> if the record ends with some string.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.CommonSelector.DetailIfEnclosed">
            <summary>Parse the current record as <b>Detail</b> if the record begins and ends with some string.</summary>
        </member>
        <member name="T:FileHelpers.MasterDetail.MasterDetailEngine">
            <summary>
            Read a master detail file, eg Orders followed by detail records
            </summary>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine.#ctor(System.Type,System.Type)">
            <summary>
			 Initializes a new instance of the MasterDetailEngine class with the specified type of records.
		</summary>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine.#ctor(System.Type,System.Type,FileHelpers.MasterDetail.MasterDetailSelector)">
            <summary>
			 Initializes a new instance of the MasterDetailEngine class with the specified type of records.
		</summary>
            <param name="masterType">The master record class.</param>
            <param name="detailType">The detail record class.</param>
            <param name="recordSelector">The <see cref="T:FileHelpers.MasterDetail.MasterDetailSelector" /> to get the <see cref="T:FileHelpers.MasterDetail.RecordAction" /> (only for read operations)</param>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine.#ctor(System.Type,System.Type,FileHelpers.MasterDetail.CommonSelector,System.String)">
            <summary>
			 Initializes a new instance of the MasterDetailEngine class with the specified type of records.
		</summary>
            <param name="masterType">The master record class.</param>
            <param name="detailType">The detail record class.</param>
            <param name="action">The <see cref="T:FileHelpers.MasterDetail.CommonSelector" /> used by the engine (only for read operations)</param>
            <param name="selector">The string passed as the selector.</param>
        </member>
        <member name="T:FileHelpers.MasterDetail.MasterDetailEngine`2">
            <summary>
			<para><b>One of the main classes of the library.</b></para>
			<para>This engine is responsible for reading and writing <b>Master-Details</b> records to or from files or streams.</para>
		</summary><remarks>
		 <para>You can set the <see cref="P:FileHelpers.ErrorManager.ErrorMode" /> of this class to
         defined how to handle an error.
         Errors can be retrieved with the <see cref="P:FileHelpers.ErrorManager.Errors" /> property if set appropriately.</para>
		 <para>See in the <a href="http://www.filehelpers.net/diagrams/">Class Diagram</a> and
         in the <a href="http://www.filehelpers.net/quickstart/">Quick Start Guide</a> for more Info.</para>
		 <para>Or you can browse the <a href="http://www.filehelpers.net/examples/">Examples Section</a> for more code.</para>
		</remarks><seealso href="http://www.filehelpers.net/quickstart/">Quick Start Guide</seealso><seealso href="http://www.filehelpers.net/diagrams/">Class Diagram</seealso><seealso href="http://www.filehelpers.net/examples/">Examples of Use</seealso><seealso href="http://www.filehelpers.net/attributes/">Attributes List</seealso>
            <!-- No matching elements were found for the following include tag --><include file="Examples.xml" path="doc/examples/MasterDetailEngine/*" />
            <typeparam name="TMaster">The Master Record Type</typeparam>
            <typeparam name="TDetail">The Detail Record Type</typeparam>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.#ctor">
            <!-- No matching elements were found for the following include tag --><include file="MasterDetailEngine.docs.xml" path="doc/MasterDetailEngineCtr1/*" />
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.#ctor(FileHelpers.MasterDetail.MasterDetailSelector)">
            <!-- No matching elements were found for the following include tag --><include file="MasterDetailEngine.docs.xml" path="doc/MasterDetailEngineCtr1/*" />
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.#ctor(System.Type,System.Type,FileHelpers.MasterDetail.MasterDetailSelector)">
            <!-- No matching elements were found for the following include tag --><include file="MasterDetailEngine.docs.xml" path="doc/MasterDetailEngineCtr1/*" />
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.#ctor(FileHelpers.MasterDetail.CommonSelector,System.String)">
            <!-- No matching elements were found for the following include tag --><include file="MasterDetailEngine.docs.xml" path="doc/MasterDetailEngineCtr2/*" />
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.#ctor(System.Type,System.Type,FileHelpers.MasterDetail.CommonSelector,System.String)">
            <!-- No matching elements were found for the following include tag --><include file="MasterDetailEngine.docs.xml" path="doc/MasterDetailEngineCtr2/*" />
        </member>
        <member name="P:FileHelpers.MasterDetail.MasterDetailEngine`2.MasterOptions">
            <summary>
            Allows you to change some record layout options at runtime
            </summary>
        </member>
        <member name="P:FileHelpers.MasterDetail.MasterDetailEngine`2.MasterType">
            <summary>
            the type of the master records handled by this engine.
            </summary>
        </member>
        <member name="P:FileHelpers.MasterDetail.MasterDetailEngine`2.RecordSelector">
            <summary>
            The <see cref="T:FileHelpers.MasterDetail.MasterDetailSelector" /> to get the <see cref="T:FileHelpers.MasterDetail.RecordAction" /> (only for read operations)
            </summary>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.ReadFile(System.String)">
            <summary>
		Read a file and return an array of the contained records.
		</summary><remarks>
		This method opens, reads and closes the file (don't open or close the file before or after calling this method)
		</remarks><example>
You need to define a Selector Method too:

		<code>
RecordAction ExampleSelector(string record)
{
   if (Char.IsLetter(record[0]))
      return RecordAction.Master;
    else
      return RecordAction.Detail;
}
                      
		</code>
Finally you must to instantiate a MasterDetailEngine and Read/Write files:

		<code>
MaterDetailEngine engine = new MaterDetailEngine(typeof(Customers), typeof(Orders), new MasterDetailSelector(ExampleSelector));

// to Read use:
MasterDetail[] res = engine.ReadFile("TestIn.txt");

// to Write use:
engine.WriteFile("TestOut.txt", res);

		</code>
		</example><param name="fileName">The file path to be read.</param><returns>An array of the records in the file</returns>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.ReadStream(System.IO.TextReader)">
            <summary>
		Read a Stream and return an array of the contained records.
		</summary><remarks>
		This method only uses the stream and does not close it after using it, you must do it.
		</remarks><example>
You need to define a Selector Method too:

		<code>
RecordAction ExampleSelector(string record)
{
   if (Char.IsLetter(record[0]))
      return RecordAction.Master;
    else
      return RecordAction.Detail;
}
                      
		</code>
Finally you must to instantiate a MasterDetailEngine and Read/Write files:

		<code>
MaterDetailEngine engine = new MaterDetailEngine(typeof(Customers), typeof(Orders), new MasterDetailSelector(ExampleSelector));

// to Read use:
MasterDetail[] res = engine.ReadFile("TestIn.txt");

// to Write use:
engine.WriteFile("TestOut.txt", res);

		</code>
		</example><param name="reader">The reader of the source stream.</param><returns>An array of the records in the Stream</returns>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.ReadString(System.String)">
            <summary>
		Read a String and return an array of the contained records.
		</summary><param name="source">The string that contains the records.</param><returns>An array of the records in the String.</returns>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.WriteFile(System.String,System.Collections.Generic.IEnumerable{FileHelpers.MasterDetail.MasterDetails{`0,`1}})">
            <summary>
		Write an array of records to the specified file.
		</summary><remarks>
		<para>This method opens, writes and closes the file
        (don't open or close the file before or after calling this method)</para>
		<para>This method over writes any existing file.</para>
		</remarks><example>
You need to define a Selector Method too:

		<code>
RecordAction ExampleSelector(string record)
{
   if (Char.IsLetter(record[0]))
      return RecordAction.Master;
    else
      return RecordAction.Detail;
}
                      
		</code>
Finally you must to instantiate a MasterDetailEngine and Read/Write files:

		<code>
MaterDetailEngine engine = new MaterDetailEngine(typeof(Customers), typeof(Orders), new MasterDetailSelector(ExampleSelector));

// to Read use:
MasterDetail[] res = engine.ReadFile("TestIn.txt");

// to Write use:
engine.WriteFile("TestOut.txt", res);

		</code>
		</example><param name="fileName">The file path to be write.</param><param name="records">The array of records to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.WriteFile(System.String,System.Collections.Generic.IEnumerable{FileHelpers.MasterDetail.MasterDetails{`0,`1}},System.Int32)">
            <summary>
		Write the specified number of records from the array to a file.
		</summary><remarks>
		<para>This method opens, writes and closes the file
        (don't open or close the file before or after calling this method)</para>
		<para>This method over writes existing files.</para>
		</remarks><example>
You need to define a Selector Method too:

		<code>
RecordAction ExampleSelector(string record)
{
   if (Char.IsLetter(record[0]))
      return RecordAction.Master;
    else
      return RecordAction.Detail;
}
                      
		</code>
Finally you must to instantiate a MasterDetailEngine and Read/Write files:

		<code>
MaterDetailEngine engine = new MaterDetailEngine(typeof(Customers), typeof(Orders), new MasterDetailSelector(ExampleSelector));

// to Read use:
MasterDetail[] res = engine.ReadFile("TestIn.txt");

// to Write use:
engine.WriteFile("TestOut.txt", res);

		</code>
		</example><param name="fileName">The file path to be write.</param><param name="records">The array of records to write.</param><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.WriteStream(System.IO.TextWriter,System.Collections.Generic.IEnumerable{FileHelpers.MasterDetail.MasterDetails{`0,`1}})">
            <summary>
		Write an array of records to the specified Stream.
		</summary><remarks>
		This method only uses the stream and does not close it after using it, you must do it.
		</remarks><example>
You need to define a Selector Method too:

		<code>
RecordAction ExampleSelector(string record)
{
   if (Char.IsLetter(record[0]))
      return RecordAction.Master;
    else
      return RecordAction.Detail;
}
                      
		</code>
Finally you must to instantiate a MasterDetailEngine and Read/Write files:

		<code>
MaterDetailEngine engine = new MaterDetailEngine(typeof(Customers), typeof(Orders), new MasterDetailSelector(ExampleSelector));

// to Read use:
MasterDetail[] res = engine.ReadFile("TestIn.txt");

// to Write use:
engine.WriteFile("TestOut.txt", res);

		</code>
		</example><param name="writer">The writer of the source stream.</param><param name="records">The array of records to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.WriteStream(System.IO.TextWriter,System.Collections.Generic.IEnumerable{FileHelpers.MasterDetail.MasterDetails{`0,`1}},System.Int32)">
            <summary>
		Write the specified number of records in the array to the Stream.
		</summary><remarks>
		This method only uses the stream and does not close it after using it, you must do it.
		</remarks><example>
You need to define a Selector Method too:

		<code>
RecordAction ExampleSelector(string record)
{
   if (Char.IsLetter(record[0]))
      return RecordAction.Master;
    else
      return RecordAction.Detail;
}
                      
		</code>
Finally you must to instantiate a MasterDetailEngine and Read/Write files:

		<code>
MaterDetailEngine engine = new MaterDetailEngine(typeof(Customers), typeof(Orders), new MasterDetailSelector(ExampleSelector));

// to Read use:
MasterDetail[] res = engine.ReadFile("TestIn.txt");

// to Write use:
engine.WriteFile("TestOut.txt", res);

		</code>
		</example><param name="writer">The writer of the source stream.</param><param name="records">The array of records to write.</param><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.WriteString(System.Collections.Generic.IEnumerable{FileHelpers.MasterDetail.MasterDetails{`0,`1}})">
            <summary>
		Write an array of records to an String and return it.
		</summary><param name="records">The array of records to write.</param><returns>The resulting string after write the records.</returns>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.WriteString(System.Collections.Generic.IEnumerable{FileHelpers.MasterDetail.MasterDetails{`0,`1}},System.Int32)">
            <summary>
		Write an array of records to an String and return it.
		</summary><param name="records">The array of records to write.</param><returns>The resulting string after write the records.</returns><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.AppendToFile(System.String,FileHelpers.MasterDetail.MasterDetails{`0,`1})">
            <summary>
		Append a record to the specified file.
		</summary><remarks>
		This method opens, seek to the end, writes and closes the file
        (don't open or close the file before or after calling this method)
		</remarks><example>
You need to define a Selector Method too:

		<code>
RecordAction ExampleSelector(string record)
{
   if (Char.IsLetter(record[0]))
      return RecordAction.Master;
    else
      return RecordAction.Detail;
}
                      
		</code>
Finally you must to instantiate a MasterDetailEngine and Read/Write files:

		<code>
MaterDetailEngine engine = new MaterDetailEngine(typeof(Customers), typeof(Orders), new MasterDetailSelector(ExampleSelector));

// to Read use:
MasterDetail[] res = engine.ReadFile("TestIn.txt");

// to Write use:
engine.WriteFile("TestOut.txt", res);

		</code>
		</example><param name="fileName">The file path to be written at end.</param><param name="record">The record to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetailEngine`2.AppendToFile(System.String,System.Collections.Generic.IEnumerable{FileHelpers.MasterDetail.MasterDetails{`0,`1}})">
            <summary>
		Append an array of records to the specified file.
		</summary><remarks>
		This method opens, seek to the end, writes and closes the file
        (don't open or close the file before or after calling this method)
		</remarks><example>
You need to define a Selector Method too:

		<code>
RecordAction ExampleSelector(string record)
{
   if (Char.IsLetter(record[0]))
      return RecordAction.Master;
    else
      return RecordAction.Detail;
}
                      
		</code>
Finally you must to instantiate a MasterDetailEngine and Read/Write files:

		<code>
MaterDetailEngine engine = new MaterDetailEngine(typeof(Customers), typeof(Orders), new MasterDetailSelector(ExampleSelector));

// to Read use:
MasterDetail[] res = engine.ReadFile("TestIn.txt");

// to Write use:
engine.WriteFile("TestOut.txt", res);

		</code>
		</example><param name="fileName">The file path to be written at end.</param><param name="records">The array of records to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="T:FileHelpers.MasterDetail.MasterDetails">
            <summary>
            Records are read which one is the master and the following records
            are details, eg an order and the items ordered.
            </summary>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetails.#ctor(System.Object,System.Object[])">
            <summary>
            records which have master and details
            </summary>
            <param name="master">Master record</param>
            <param name="details">Collection of detail records</param>
        </member>
        <member name="T:FileHelpers.MasterDetail.MasterDetails`2">
            <summary>
            <para>This class contains information of a Master record and its Details records.</para>
            <para>This class is used for the Read and Write operations of the <see cref="T:FileHelpers.MasterDetail.MasterDetailEngine"/>.</para>
            </summary>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetails`2.#ctor">
            <summary>Create an empty instance.</summary>
        </member>
        <member name="M:FileHelpers.MasterDetail.MasterDetails`2.#ctor(`0,`1[])">
            <summary>Create a new instance with the specified values.</summary>
            <param name="master">The master record.</param>
            <param name="details">The details record.</param>
        </member>
        <member name="F:FileHelpers.MasterDetail.MasterDetails`2.mEmpty">
            <summary>The canonical empty MasterDetail object.</summary>
        </member>
        <member name="P:FileHelpers.MasterDetail.MasterDetails`2.Empty">
            <summary>Returns a canonical empty MasterDetail object.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.MasterDetails`2.mMaster">
            <summary>
            Master record for this group
            </summary>
        </member>
        <member name="P:FileHelpers.MasterDetail.MasterDetails`2.Master">
            <summary>The Master record.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.MasterDetails`2.mDetails">
            <summary>
            An Array with the Detail records.
            </summary>
        </member>
        <member name="P:FileHelpers.MasterDetail.MasterDetails`2.Details">
            <summary>An Array with the Detail records.</summary>
        </member>
        <member name="T:FileHelpers.MasterDetail.RecordAction">
            <summary>
            Used with the <see cref="T:FileHelpers.MasterDetail.MasterDetailSelector"/> Delegate
            to determines the action used by the <see cref="T:FileHelpers.MasterDetail.MasterDetailEngine"/>
            </summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.RecordAction.Skip">
            <summary><b>Ignore</b> the current record.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.RecordAction.Master">
            <summary>Use the current record as <b>Master</b>.</summary>
        </member>
        <member name="F:FileHelpers.MasterDetail.RecordAction.Detail">
            <summary>Use the current record as <b>Detail</b>.</summary>
        </member>
        <member name="T:FileHelpers.RecordTypeSelector">
            <summary>
            Delegate that determines the Type of the current record
            </summary>
            <param name="recordString">The string of the current record.</param>
            <param name="engine">The engine that calls the selector.</param>
            <returns>The type used for the current record</returns>
        </member>
        <member name="T:FileHelpers.MultiRecordEngine">
            <summary>
            <para>This engine allows you to parse and write files that contain
            records of different types and that are in a linear relationship</para>
            <para>(for Master-Detail check the <see cref="T:FileHelpers.MasterDetail.MasterDetailEngine"/>)</para>
            </summary>
        </member>
        <member name="P:FileHelpers.MultiRecordEngine.RecordSelector">
            <summary>
            The Selector used by the engine in Read operations to determine the Type to use.
            </summary>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.#ctor(System.Type[])">
            <summary>Create a new instance of the MultiRecordEngine</summary>
            <param name="recordTypes">The Types of the records that this engine can handle.</param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.#ctor(FileHelpers.RecordTypeSelector,System.Type[])">
            <summary>Create a new instance of the MultiRecordEngine</summary>
            <param name="recordTypes">The Types of the records that this engine can handle.</param>
            <param name="recordSelector">
            Used only in read operations. The selector indicates to the engine
            what Type to use in each read line.
            </param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.ReadFile(System.String)">
            <summary>
            Reads a file and returns the records.
            </summary>
            <param name="fileName">The file with the records.</param>
            <returns>The read records of different types all mixed.</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.ReadStream(System.IO.TextReader)">
            <summary>
            Read an array of objects from a stream
            </summary>
            <param name="reader">Stream we are reading from</param>
            <returns>Array of objects</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.ReadStream(FileHelpers.IRecordReader)">
            <summary>
		Read a Stream and return an array of the contained records.
		</summary><remarks>
		This method only uses the stream and does not close the stream after using it, you must do it.
		</remarks><include file="Examples.xml" path="doc/examples/ReadFile/*" /><param name="reader">The reader of the source stream.</param><returns>An array of the records in the Stream</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.ReadString(System.String)">
            <summary>
		Read a String and return an array of the contained records.
		</summary><param name="source">The string that contains the records.</param><returns>An array of the records in the String.</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.WriteFile(System.String,System.Collections.IEnumerable)">
            <summary>
		Write an array of records to the specified file.
		</summary><remarks>
			<para>This method opens, writes and closes the file
			(don't open or close the file before or after calling this method)</para>
		<para>This method over writes existing files.</para>
		</remarks><include file="Examples.xml" path="doc/examples/WriteFile/*" /><param name="fileName">The file path to be write.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.WriteFile(System.String,System.Collections.IEnumerable,System.Int32)">
            <summary>
		Write the specified number of records from the array to a file.
		</summary><remarks>
			<para>This method opens, writes and closes the file
			(don't open or close the file before or after calling this method)</para>
		<para>This method over writes existing files.</para>
		</remarks><include file="Examples.xml" path="doc/examples/WriteFile/*" /><param name="fileName">The file path to be write.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.WriteStream(System.IO.TextWriter,System.Collections.IEnumerable)">
            <summary>
            Write the records to a file
            </summary>
            <param name="writer">Where data is written</param>
            <param name="records">records to write to the file</param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.WriteStream(System.IO.TextWriter,System.Collections.IEnumerable,System.Int32)">
            <summary>
		Write the specified number of records in the array to the Stream.
		</summary><remarks>
		This method only uses the stream and does not close the stream after using it, you must do it.
		</remarks><include file="Examples.xml" path="doc/examples/WriteFile/*" /><param name="writer">The writer of the source stream.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.WriteString(System.Collections.IEnumerable)">
            <summary>
		Write an array of records to an String and return it.
		</summary><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>The resulting string after write the records.</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.WriteString(System.Collections.IEnumerable,System.Int32)">
            <summary>
		Write an array of records to an String and return it.
		</summary><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>The resulting string after write the records.</returns><param name="maxRecords">The max number of array elements to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.AppendToFile(System.String,System.Object)">
            <summary>
		Append a record to the specified file.
		</summary><remarks>
			This method opens, seeks to the end, writes and closes the file
			(don't open or close the file before or after calling this method)
		</remarks><include file="Examples.xml" path="doc/examples/AppendFile/*" /><param name="fileName">The file path to be written at end.</param><param name="record">The record to write.</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.AppendToFile(System.String,System.Collections.IEnumerable)">
            <summary>
		Append an array of records to the specified file.
		</summary><remarks>
			This method opens, seeks to the end, writes and closes the file
			(don't open or close the file before or after calling this method)
		</remarks><include file="Examples.xml" path="doc/examples/AppendFile/*" /><param name="fileName">The file path to be written at end.</param><param name="records">The records to write (Can be an array, ArrayList, etc)</param><returns>True if the operation is successful. False otherwise.</returns>
        </member>
        <member name="P:FileHelpers.MultiRecordEngine.LastRecord">
            <!-- Failed to insert some or all of included XML --><include file="FileHelperAsyncEngine.docs.xml" path="doc/LastRecord/*" />
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.BeginReadStream(System.IO.TextReader)">
            <summary>
            Read a generic file as delimited by newlines
            </summary>
            <param name="reader">Text reader</param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.BeginReadStream(FileHelpers.IRecordReader)">
            <summary>
            Method used to use this engine in Async mode. Work together with
            <see cref="M:FileHelpers.MultiRecordEngine.ReadNext"/>. (Remember to call Close after read the
            data)
            </summary>
            <param name="reader">The source Reader.</param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.BeginReadFile(System.String)">
            <summary>
            Method used to use this engine in Async mode. Work together with
            <see cref="M:FileHelpers.MultiRecordEngine.ReadNext"/>. (Remember to call Close after read the
            data)
            </summary>
            <param name="fileName">The source file.</param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.BeginReadString(System.String)">
            <summary>
            Method used to use this engine in Async mode. Work together with
            <see cref="M:FileHelpers.MultiRecordEngine.ReadNext"/>. (Remember to call Close after read the
            data)
            </summary>
            <param name="sourceData">The source String</param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.Flush">
            <summary>
            Save all the buffered data for write to the disk. 
            Useful to long opened async engines that wants to save pending
            values or for engines used for logging.
            </summary>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.Close">
            <summary>
            Close the underlining Readers and Writers. (if any)
            </summary>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.ReadNext">
            <summary>
            Reads the next record from the source
            </summary>
            <returns>The record just read</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.ReadNexts(System.Int32)">
            <summary>
            Read a defined number of records from the source
            </summary>
            <param name="numberOfRecords">The count of records to read</param>
            <returns>An Array with all the read record objects</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.System#Collections#IEnumerable#GetEnumerator">
            <summary>Allows to loop record by record in the engine</summary>
            <returns>The Enumerator</returns>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.System#IDisposable#Dispose">
            <summary>
            Release Resources
            </summary>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.Finalize">
            <summary>Destructor</summary>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.WriteNext(System.Object)">
            <summary>
            Write the next record to a file or stream opened with
            <see cref="M:FileHelpers.MultiRecordEngine.BeginWriteFile(System.String)" />, <see cref="M:FileHelpers.MultiRecordEngine.BeginWriteStream(System.IO.TextWriter)" /> or
            <see cref="M:FileHelpers.MultiRecordEngine.BeginAppendToFile(System.String)" /> method.
            </summary>
            <param name="record">The record to write.</param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.WriteNexts(System.Collections.IEnumerable)">
            <summary>
            Write the nexts records to a file or stream opened with
            <see cref="M:FileHelpers.MultiRecordEngine.BeginWriteFile(System.String)" />, <see cref="M:FileHelpers.MultiRecordEngine.BeginWriteStream(System.IO.TextWriter)" /> or
            <see cref="M:FileHelpers.MultiRecordEngine.BeginAppendToFile(System.String)" /> method.
            </summary>
            <param name="records">The records to write (Can be an array, ArrayList, etc)</param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.BeginWriteStream(System.IO.TextWriter)">
            <summary>Set the stream to be used in the <see cref="M:FileHelpers.MultiRecordEngine.WriteNext(System.Object)" /> operation.</summary>
            <remarks>
            <para>When you finish to write to the file you must call
            <b><see cref="M:FileHelpers.MultiRecordEngine.Close" /></b> method.</para>
            </remarks>
            <param name="writer">To stream to writes to.</param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.BeginWriteFile(System.String)">
            <summary>
            Open a file for write operations. If exist the engine override it.
            You can use <see cref="M:FileHelpers.MultiRecordEngine.WriteNext(System.Object)"/> or <see cref="M:FileHelpers.MultiRecordEngine.WriteNexts(System.Collections.IEnumerable)"/> to
            write records.
            </summary>
            <remarks>
            <para>When you finish to write to the file you must call
            <b><see cref="M:FileHelpers.MultiRecordEngine.Close" /></b> method.</para>
            </remarks>
            <param name="fileName">The file path to be opened for write.</param>
        </member>
        <member name="M:FileHelpers.MultiRecordEngine.BeginAppendToFile(System.String)">
            <summary>Open a file to be appended at the end.</summary>
            <remarks><para>This method open and seek to the end the file.</para>
            <para>When you finish to append to the file you must call
            <b><see cref="M:FileHelpers.MultiRecordEngine.Close" /></b> method.</para></remarks>
            <param name="fileName">The file path to be opened to write at the end.</param>
        </member>
        <member name="T:FileHelpers.BigFileSorter">
            <summary>
            This class help to sort really big files using the External Sorting algorithm
            http://en.wikipedia.org/wiki/External_sorting
            </summary>
        </member>
        <member name="T:FileHelpers.BigFileSorter.SorterRecord">
            <summary>
            Support record class for string sorting
            </summary>
        </member>
        <member name="F:FileHelpers.BigFileSorter.SorterRecord.Value">
            <summary>
            Value of the string
            </summary>
        </member>
        <member name="M:FileHelpers.BigFileSorter.SorterRecord.CompareTo(FileHelpers.BigFileSorter.SorterRecord)">
            <summary>
            Compare to the sorter record
            </summary>
            <param name="other">Record to compare</param>
            <returns>Comparison</returns>
        </member>
        <member name="M:FileHelpers.BigFileSorter.#ctor">
            <summary>
            Create a big file sorter with the minimum buffer size
            </summary>
        </member>
        <member name="M:FileHelpers.BigFileSorter.#ctor(System.Int32)">
            <summary>
            Create a big file sorter using the block size
            </summary>
            <param name="blockFileSizeInBytes">Block size to work on</param>
        </member>
        <member name="M:FileHelpers.BigFileSorter.#ctor(System.Text.Encoding)">
            <summary>
            Create a big file sorter using and encoding
            </summary>
            <param name="encoding">Encoding of the file</param>
        </member>
        <member name="M:FileHelpers.BigFileSorter.#ctor(System.Comparison{System.String})">
            <summary>
            Create a big file sorter using comparison operator
            </summary>
            <param name="sorter">Comparison operator</param>
        </member>
        <member name="M:FileHelpers.BigFileSorter.#ctor(System.Comparison{System.String},System.Int32)">
            <summary>
            Create a big file sorter with a comparison operator and block size
            </summary>
            <param name="sorter">Comparison operator</param>
            <param name="blockFileSizeInBytes">Block size to work on file</param>
        </member>
        <member name="M:FileHelpers.BigFileSorter.#ctor(System.Comparison{System.String},System.Text.Encoding,System.Int32)">
            <summary>
            Create a bug file sorter
            </summary>
            <param name="sorter">Comparison operator</param>
            <param name="encoding">encoding of the file</param>
            <param name="blockFileSizeInBytes">Block size to work on</param>
        </member>
        <member name="M:FileHelpers.BigFileSorter.CreateSorter(System.Comparison{System.String})">
            <summary>
            Create a record sorter based on string value
            </summary>
            <param name="sorter">string sorter to convert</param>
            <returns>new record comparison sorter</returns>
        </member>
        <member name="T:FileHelpers.BigFileSorter`1">
            <summary>
            This class help to sort really big files using the External Sorting algorithm
            http://en.wikipedia.org/wiki/External_sorting
            </summary>
            <typeparam name="T">Type of record to sort</typeparam>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.#cctor">
            <summary>
            set the default encoding to the default encoding for C#
            </summary>
        </member>
        <member name="F:FileHelpers.BigFileSorter`1.DefaultBlockSize">
            <summary>
            Default block size,  10 meg
            </summary>
        </member>
        <member name="F:FileHelpers.BigFileSorter`1.MaxBufferSize">
            <summary>
            Maximum block size,  50 meg
            </summary>
        </member>
        <member name="F:FileHelpers.BigFileSorter`1.MinBlockSize">
            <summary>
            Minimum block size 0.5 meg
            </summary>
        </member>
        <member name="F:FileHelpers.BigFileSorter`1.AutoBlockSizeRatio">
            <summary>
            Ratio of file size to block size, used when auto assigning block size
            </summary>
        </member>
        <member name="F:FileHelpers.BigFileSorter`1.mSorter">
            <summary>
            Comparison operator for this sort
            </summary>
        </member>
        <member name="F:FileHelpers.BigFileSorter`1.AutoSetBlockSize">
            <summary>
            Indicates if the block size should be determined based on file size
            </summary>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.#ctor">
            <summary>
            Sort big files using the External Sorting algorithm
            </summary>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.#ctor(System.Int32)">
            <summary>
            Sort a large file
            </summary>
            <param name="blockFileSizeInBytes">block size for sort to work on</param>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.#ctor(System.Text.Encoding)">
            <summary>
            Large file sorter
            </summary>
            <param name="encoding">Encoding of the file</param>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.#ctor(System.Text.Encoding,System.Int32)">
            <summary>
            Large file sorter
            </summary>
            <param name="encoding">Encoding of the file</param>
            <param name="blockFileSizeInBytes">Size of the blocks in bytes</param>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.#ctor(System.Comparison{`0},System.Text.Encoding,System.Int32)">
            <summary>
            Large file sorter specifying the comparison operator
            </summary>
            <param name="sorter">Comparison operator</param>
            <param name="encoding">Encoding of the file</param>
            <param name="blockFileSizeInBytes">Block size to work on</param>
        </member>
        <member name="P:FileHelpers.BigFileSorter`1.TempDirectory">
            <summary>
            The directory for the temp files (by the default the same directory
            as sourceFile)
            </summary>
        </member>
        <member name="P:FileHelpers.BigFileSorter`1.BlockFileSizeInBytes">
            <summary>
            The Size of each block that will be sorted in memory which are
            later merged together
            </summary>
        </member>
        <member name="P:FileHelpers.BigFileSorter`1.DeleteTempFiles">
            <summary>
            Indicates if the temporary files will be deleted (True by default)
            </summary>
        </member>
        <member name="P:FileHelpers.BigFileSorter`1.Encoding">
            <summary> The Encoding used to read and write the files </summary>
        </member>
        <member name="P:FileHelpers.BigFileSorter`1.RunGcCollectForEachPart">
            <summary> Indicates if the Sorter run a GC.Collect() after sort and write each part. Default is true.</summary>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.Sort(System.String,System.String)">
            <summary>
            Sort a file from one filename to another filename
            </summary>
            <remarks>Handles very large files</remarks>
            <param name="sourceFile">File to read in</param>
            <param name="destinationFile">File to write out</param>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.BlockSizeFromFileSize(System.String)">
            <summary>
            Returns a block size based on the source file size
            </summary>
            <param name="sourceFile">Source file path</param>
            <returns></returns>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.CreateStream(System.String,System.Int32)">
            <summary>
            Create a StreamWriter given the filename and buffer size
            </summary>
            <param name="filename">Filename to write</param>
            <param name="bufferSize">Buffer size</param>
            <returns>StreamWriter object</returns>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.GetSplitName(System.String,System.Int32)">
            <summary>
            Create a part of a file,  files are split into chunks for sort
            processing
            </summary>
            <param name="file">Filename (prefix)</param>
            <param name="splitNum">This chunk number</param>
            <returns>revised filename to write</returns>
        </member>
        <member name="M:FileHelpers.BigFileSorter`1.MergeTheChunks(FileHelpers.SortQueue{`0}[],System.String,System.String,System.String)">
            <summary>
            Large files are sorted in chunk then merged together in the final process
            </summary>
            <param name="queues">list of chunks to merge</param>
            <param name="destinationFile">output filename</param>
            <param name="headerText">The header of the destinationFile</param>
            <param name="footerText">The footer of the destinationFile</param>
        </member>
        <member name="T:FileHelpers.SortQueue`1">
            <summary>
            One sorted 'chunk' of an input file.
            </summary>
            <typeparam name="T">object type we are sorting</typeparam>
        </member>
        <member name="M:FileHelpers.SortQueue`1.#ctor(System.Text.Encoding,System.String,System.Boolean)">
             <summary>
            
             </summary>
             <param name="encoding">encoding of the file</param>
             <param name="file">filename of the chunk</param>
             <param name="deleteFile">do we remove file after afterwards</param>
        </member>
        <member name="M:FileHelpers.SortQueue`1.MoveNext">
            <summary>
            Move to the next record along, sets current
            </summary>
        </member>
        <member name="M:FileHelpers.SortQueue`1.Dispose">
            <summary>
            close engine and if requested delete the file
            </summary>
        </member>
        <member name="T:FileHelpers.Streams.InternalStreamReader">
            <summary>
            Encapsulate stream reader provide some extra caching, and byte by byte
            read
            </summary>
        </member>
        <member name="M:FileHelpers.Streams.InternalStreamReader.#ctor(System.String,System.Text.Encoding,System.Boolean,System.Int32)">
            <summary>
            Open a file for reading allowing encoding, detecting type and buffer size
            </summary>
            <param name="path">Filename to read</param>
            <param name="encoding">Encoding of file,  eg UTF8</param>
            <param name="detectEncodingFromByteOrderMarks">Detect type of file from contents</param>
            <param name="bufferSize">Buffer size for the read</param>
        </member>
        <member name="M:FileHelpers.Streams.InternalStreamReader.Close">
            <summary>
            Close the stream, cleanup
            </summary>
        </member>
        <member name="M:FileHelpers.Streams.InternalStreamReader.DetectEncoding">
            <summary>
            Open the file and check the first few bytes for Unicode encoding
            values
            </summary>
        </member>
        <member name="M:FileHelpers.Streams.InternalStreamReader.DiscardBufferedData">
            <summary>
            Discard all data inside the internal buffer
            </summary>
        </member>
        <member name="M:FileHelpers.Streams.InternalStreamReader.Dispose(System.Boolean)">
            <summary>
            clean up the stream object
            </summary>
            <param name="disposing">first call or second</param>
        </member>
        <member name="M:FileHelpers.Streams.InternalStreamReader.Peek">
            <summary>
            Return the byte at the current position
            </summary>
            <returns>byte at current position or -1 on error</returns>
        </member>
        <member name="M:FileHelpers.Streams.InternalStreamReader.Read">
            <summary>
            Read a byte from the stream
            </summary>
            <returns></returns>
        </member>
        <member name="P:FileHelpers.Streams.InternalStreamReader.Position">
            <summary>
            Position within the file
            </summary>
        </member>
        <member name="P:FileHelpers.Streams.InternalStreamReader.Closable">
            <summary>
            Is the stream able to be closed
            </summary>
        </member>
        <member name="P:FileHelpers.Streams.InternalStreamReader.CurrentEncoding">
            <summary>
            What is the streams current encoding
            </summary>
        </member>
        <member name="P:FileHelpers.Streams.InternalStreamReader.BaseStream">
            <summary>
            What is the underlying stream on input file
            </summary>
        </member>
        <member name="P:FileHelpers.Streams.InternalStreamReader.EndOfStream">
            <summary>
            Check that the stream has ended,  all data read
            </summary>
        </member>
        <member name="T:FileHelpers.Streams.InternalStringReader">
            <summary>
            Enable reading a string as a stream
            </summary>
        </member>
        <member name="F:FileHelpers.Streams.InternalStringReader.mS">
            <summary>
            String we are analysing
            </summary>
        </member>
        <member name="M:FileHelpers.Streams.InternalStringReader.#ctor(System.String)">
            <summary>
            Create a stream based on the string
            </summary>
            <param name="s"></param>
        </member>
        <member name="P:FileHelpers.Streams.InternalStringReader.Length">
            <summary>
            Length of the stream
            </summary>
        </member>
        <member name="P:FileHelpers.Streams.InternalStringReader.Position">
            <summary>
            Position within the stream
            </summary>
        </member>
        <member name="M:FileHelpers.Streams.InternalStringReader.Close">
            <summary>
            Close the stream
            </summary>
        </member>
        <member name="M:FileHelpers.Streams.InternalStringReader.Dispose(System.Boolean)">
            <summary>
            Close the stream and release resources
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:FileHelpers.Streams.InternalStringReader.Peek">
            <summary>
            Peek at the next byte along in the stream
            </summary>
            <returns>Next character as an integer</returns>
        </member>
        <member name="M:FileHelpers.Streams.InternalStringReader.Read">
            <summary>
            Read the next byte along in the string
            </summary>
            <returns>next character as an integer</returns>
        </member>
        <member name="M:FileHelpers.Streams.InternalStringReader.Read(System.Char[],System.Int32,System.Int32)">
            <summary>
            Replaces text into the buffer at index for count characters
            </summary>
            <param name="buffer">
            An array of Unicode characters to which characters in this instance
            are copied.
            </param>
            <param name="index">
            The index in destination at which the copy operation begins.
            </param>
            <param name="count">
            The number of characters in this instance to copy to destination.
            </param>
            <returns>number of characters actually copied</returns>
        </member>
        <member name="M:FileHelpers.Streams.InternalStringReader.ReadLine">
            <summary>
            Read a line from the buffer
            </summary>
            <returns>Line without line end</returns>
        </member>
        <member name="M:FileHelpers.Streams.InternalStringReader.ReadToEnd">
            <summary>
            Read the buffer to the end
            </summary>
            <returns>String containing all remaining text</returns>
        </member>
        <member name="T:FileHelpers.Streams.StreamInfoProvider">
            <summary>
            Calculate statistics on stream,  position and total size
            </summary>
        </member>
        <member name="T:FileHelpers.Streams.StreamInfoProvider.GetValue">
            <summary>
            Delegate to the stream values returned
            </summary>
            <returns></returns>
        </member>
        <member name="F:FileHelpers.Streams.StreamInfoProvider.mPositionCalculator">
            <summary>
            Position within the stream -1 is beginning
            </summary>
        </member>
        <member name="F:FileHelpers.Streams.StreamInfoProvider.mLength">
            <summary>
            Length of the stream -1 is unknown
            </summary>
        </member>
        <member name="M:FileHelpers.Streams.StreamInfoProvider.#ctor(System.IO.TextReader)">
            <summary>
            Provide as much information about the input stream as we can,  size
            and position
            </summary>
            <param name="reader">reader we are analysing</param>
        </member>
        <member name="P:FileHelpers.Streams.StreamInfoProvider.Position">
            <summary>
            Position within the stream
            </summary>
        </member>
        <member name="P:FileHelpers.Streams.StreamInfoProvider.TotalBytes">
            <summary>
            Total number of bytes within the stream
            </summary>
        </member>
    </members>
</doc>
