{}[2024-05-09 11:35:56.046]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:35:56.077]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:35:56.108]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:35:56.124]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:35:57.040]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 11:36:00.338]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB5G1ED6JGRVQR1[1f]0[1f]50901[1f][1f][1f][1c]1=0=0=0=0=0[1c]1166=0=0=0=0=0[1c]20240509113559[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]y
{}[2024-05-09 11:36:00.369]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 11:36:00.385]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:40:38.805]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:40:38.837]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:40:38.868]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:40:38.884]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:40:39.040]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]8.18[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]L
{}[2024-05-09 11:40:47.127]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000667[1f]000667[1f]03RLEB5YY3K7AR28QR7[1f]1[1f]050901[1f][1f][1f][1c]03[1c]818[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509114038[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091140383043[1f]TC=F74D2234D7D1C620[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00EC[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]G
{}[2024-05-09 11:40:47.158]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 11:40:47.174]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:40:47.237]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:40:47.252]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:40:47.268]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:40:47.283]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:40:47.315]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]8.18[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]e
{}[2024-05-09 11:40:51.222]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB5YZEK0TUV5QR8[1f]2[1f]050901[1f][1f][1f][1c]04[1c]818[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240509114046[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091140465481[1f]TC=F74D2234D7D1C620[1f]TVR=0000000000[1f]ATC=00EC[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]c
{}[2024-05-09 11:40:51.253]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 11:40:51.285]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:42:59.465]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:42:59.496]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:42:59.527]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:42:59.543]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:42:59.574]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 11:43:01.465]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB5ZTJUW4BLHQRB[1f]0[1f]050901[1f][1f][1f][1c]1=0=0=0=0=0[1c]818=0=0=0=0=0[1c]20240509114300[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]z
{}[2024-05-09 11:43:01.496]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 11:43:01.512]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:43:12.376]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:43:12.408]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:43:12.814]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:43:12.829]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:43:12.970]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]1.06[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]J
{}[2024-05-09 11:43:21.797]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000697[1f]000697[1f]03RLEB5TFBJG7LR9QRW[1f]1[1f]050902[1f][1f][1f][1c]03[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509114312[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091143122591[1f]TC=9D2102B3D40511D1[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00ED[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]O
{}[2024-05-09 11:43:21.828]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 11:43:21.844]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:44:29.067]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:44:29.099]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:44:29.130]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:44:29.161]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:44:29.442]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 11:44:31.216]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB5VL50YAWF5QRE[1f]0[1f]050902[1f][1f][1f][1c]0=0=0=0=0=0[1c]0=0=0=0=0=0[1c]20240509114430[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]m
{}[2024-05-09 11:44:31.248]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 11:44:31.263]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:44:41.133]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:44:41.164]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:44:41.196]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:44:41.211]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:44:41.430]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]2.12[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]L
{}[2024-05-09 11:44:48.563]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000698[1f]000698[1f]03RLEB60412ZP3XPQRG[1f]1[1f]050903[1f][1f][1f][1c]03[1c]212[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509114441[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091144415518[1f]TC=B40A638378436BCF[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00EE[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]Z
{}[2024-05-09 11:44:48.595]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 11:44:48.626]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:44:54.815]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:44:54.847]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:44:54.878]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:44:54.909]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:44:55.253]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]3.12[1c][1c]1[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]e
{}[2024-05-09 11:44:55.675]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 11:44:55.706]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 11:44:55.737]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:45:00.983]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:45:00.998]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:45:01.014]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:45:01.030]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:45:01.092]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]3.12[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]d
{}[2024-05-09 11:45:04.913]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB60K61WRFTJQRH[1f]2[1f]050903[1f][1f][1f][1c]04[1c]312[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240509114500[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091145009088[1f]TC=B40A638378436BCF[1f]TVR=0000000000[1f]ATC=00EE[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]m
{}[2024-05-09 11:45:04.944]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 11:45:04.960]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:48:57.622]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:48:57.654]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:48:57.685]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:48:57.716]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:48:57.935]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 11:48:59.920]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB67RHYELG3VQRN[1f]0[1f]050903[1f][1f][1f][1c]1=0=0=0=0=0[1c]312=0=0=0=0=0[1c]20240509114859[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03][1a]
{}[2024-05-09 11:48:59.951]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 11:48:59.982]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:52:27.794]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:52:27.825]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:52:27.857]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:52:27.888]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:52:28.091]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 11:52:28.825]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]100023[1c]NOT FOUND[03]M
{}[2024-05-09 11:52:28.857]ERROR:[POSLink.cpp-2066 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Batch_BATCHCLOSE_EDCType(NULL) Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 11:52:28.888]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:55:16.830]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:55:16.862]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:55:16.893]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:55:16.924]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:55:17.033]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]2.06[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]I
{}[2024-05-09 11:55:26.098]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000709[1f]000709[1f]03RLEB6JXLFRQNM5QZ0[1f]1[1f]050904[1f][1f][1f][1c]03[1c]206[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509115516[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091155166128[1f]TC=1AF192298A850B30[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00EF[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]I
{}[2024-05-09 11:55:26.145]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 11:55:26.177]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:55:26.239]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:55:26.270]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:55:26.302]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:55:26.333]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:55:26.364]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]2.06[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]`
{}[2024-05-09 11:55:30.187]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB6JMT5MMJH2QZ1[1f]2[1f]050904[1f][1f][1f][1c]04[1c]206[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240509115525[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091155253677[1f]TC=1AF192298A850B30[1f]TVR=0000000000[1f]ATC=00EF[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][15]
{}[2024-05-09 11:55:30.218]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 11:55:30.249]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:55:45.089]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:55:45.120]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:55:45.151]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:55:45.183]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:55:45.292]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]4.24[1c][1c]3[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]M
{}[2024-05-09 11:55:53.215]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000710[1f]000710[1f]03RLEB6KD3F2ZUAWQZ3[1f]3[1f]050904[1f][1f][1f][1c]03[1c]424[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]3[1f]3[1f]20240509115544[1f][1f][1f][1f]3[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091155441637[1f]TC=495A5BAAEFF4891A[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00F0[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]G
{}[2024-05-09 11:55:53.246]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 11:55:53.278]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:55:58.538]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:55:58.570]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:55:58.601]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:55:58.632]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:55:58.804]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]6.24[1c][1c]3[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]g
{}[2024-05-09 11:55:59.288]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 11:55:59.320]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 11:55:59.351]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:56:08.086]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:56:08.117]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:56:08.149]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:56:08.180]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:56:08.336]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]6.24[1c][1c]3[1f][1f][1f]3[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]d
{}[2024-05-09 11:56:12.149]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB6KVPD5XJ34QZ4[1f]4[1f]050904[1f][1f][1f][1c]04[1c]624[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]4[1f]3[1f]20240509115607[1f][1f][1f][1f]4[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091156079842[1f]TC=495A5BAAEFF4891A[1f]TVR=0000000000[1f]ATC=00F0[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][14]
{}[2024-05-09 11:56:12.180]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 11:56:12.211]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 11:59:55.399]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 11:59:55.430]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 11:59:55.477]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 11:59:55.508]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 11:59:55.680]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]7.42[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]L
{}[2024-05-09 12:00:03.288]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000712[1f]000712[1f]03RLEB6Z1AD2TK05QZ9[1f]5[1f]050904[1f][1f][1f][1c]03[1c]742[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]5[1f]1[1f]20240509115955[1f][1f][1f][1f]5[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091159558036[1f]TC=27D8259CF2167240[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00F1[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]*
{}[2024-05-09 12:00:03.335]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:00:03.367]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:00:08.143]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:00:08.174]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:00:08.205]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:00:08.236]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:00:08.596]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]12.42[1c][1c]1[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]P
{}[2024-05-09 12:00:09.049]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:00:09.080]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:00:09.111]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:00:14.778]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:00:14.809]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:00:14.841]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:00:14.872]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:00:15.028]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]12.42[1c][1c]1[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]P
{}[2024-05-09 12:00:15.497]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:00:15.528]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:00:15.559]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:00:30.088]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:00:30.119]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:00:30.150]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:00:30.181]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:00:30.385]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]12.42[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]Q
{}[2024-05-09 12:00:30.870]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100021[1c]ALREADY COMPL.[03]o
{}[2024-05-09 12:00:30.901]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =ALREADY COMPL.
{}[2024-05-09 12:00:30.932]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:00:39.155]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:00:39.186]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:00:39.217]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:00:39.248]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:00:39.998]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 12:00:41.873]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB6T90916NHBQZA[1f]0[1f]050904[1f][1f][1f][1c]2=0=0=0=0=0[1c]830=0=0=0=0=0[1c]20240509120041[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]l
{}[2024-05-09 12:00:41.915]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 12:00:41.941]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:00:58.974]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:00:59.005]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:00:59.052]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:00:59.083]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:00:59.255]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]2.12[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]L
{}[2024-05-09 12:01:06.075]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000713[1f]000713[1f]03RLEB6TUJQM340UQZW[1f]1[1f]050905[1f][1f][1f][1c]03[1c]212[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509120058[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091200585320[1f]TC=8D43FEC605C50BAA[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00F2[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]B
{}[2024-05-09 12:01:06.106]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:01:06.137]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:01:11.161]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:01:11.193]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:01:11.224]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:01:11.240]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:01:11.552]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]4.12[1c][1c]1[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]b
{}[2024-05-09 12:01:11.974]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:01:12.005]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:01:12.036]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:01:20.528]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:01:20.559]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:01:20.591]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:01:20.606]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:01:20.762]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]4.12[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]c
{}[2024-05-09 12:01:24.559]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB6UGUY4UPY5QZD[1f]2[1f]050905[1f][1f][1f][1c]04[1c]412[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240509120120[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091201204473[1f]TC=8D43FEC605C50BAA[1f]TVR=0000000000[1f]ATC=00F2[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][0f]
{}[2024-05-09 12:01:24.591]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 12:01:24.622]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:04:22.513]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:04:22.544]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:04:22.575]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:04:22.607]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:04:22.935]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]2.12[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]L
{}[2024-05-09 12:04:29.961]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000720[1f]000720[1f]03RLEB745RMHKB67QZX[1f]3[1f]050905[1f][1f][1f][1c]03[1c]212[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]3[1f]1[1f]20240509120422[1f][1f][1f][1f]3[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091204229304[1f]TC=7DB1B221BC5B11B7[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00F3[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]R
{}[2024-05-09 12:04:30.008]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:04:30.039]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:04:36.999]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:04:37.030]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:04:37.062]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:04:37.093]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:04:37.374]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]3.12[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]d
{}[2024-05-09 12:04:37.827]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100021[1c]ALREADY COMPL.[03]o
{}[2024-05-09 12:04:37.859]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =ALREADY COMPL.
{}[2024-05-09 12:04:37.890]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:04:42.058]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:04:42.089]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:04:42.120]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:04:42.152]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:04:42.308]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 12:04:44.132]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB74LJFGHWHHQZJ[1f]0[1f]050905[1f][1f][1f][1c]1=0=0=0=0=0[1c]412=0=0=0=0=0[1c]20240509120443[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]e
{}[2024-05-09 12:04:44.163]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 12:04:44.194]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:04:57.871]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:04:57.902]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:04:57.949]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:04:57.965]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:04:58.258]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]1.06[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]J
{}[2024-05-09 12:05:05.051]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000721[1f]000721[1f]03RLEB7581PGXYHZQZL[1f]1[1f]050906[1f][1f][1f][1c]03[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509120457[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091204575313[1f]TC=6355BBA07C7F03B4[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00F4[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]^
{}[2024-05-09 12:05:05.082]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:05:05.113]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:05:10.953]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:05:10.984]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:05:10.999]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:05:11.015]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:05:11.171]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]2.06[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]`
{}[2024-05-09 12:05:15.056]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB75HUQAXJ7GQZM[1f]2[1f]050906[1f][1f][1f][1c]04[1c]206[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240509120510[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091205108733[1f]TC=6355BBA07C7F03B4[1f]TVR=0000000000[1f]ATC=00F4[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][1b]
{}[2024-05-09 12:05:15.088]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 12:05:15.103]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:07:18.085]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:07:18.116]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:07:18.163]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:07:18.195]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:07:18.351]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]3.18[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]G
{}[2024-05-09 12:07:25.119]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000723[1f]000723[1f]03RLEB79GZ1KE0Y6QZP[1f]3[1f]050906[1f][1f][1f][1c]03[1c]318[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]3[1f]1[1f]20240509120717[1f][1f][1f][1f]3[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091207179911[1f]TC=CA1F28CD2F3BB6E4[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00F5[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]H
{}[2024-05-09 12:07:25.151]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:07:25.182]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:07:44.419]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:07:44.450]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:07:44.481]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:07:44.512]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:07:44.778]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]02[1c]3.18[1c][1c]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]G
{}[2024-05-09 12:07:53.163]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000724[1f]000724[1f]03RLEB7AW5AEKXWPQZQ[1f]4[1f]050906[1f][1f][1f][1c]02[1c]318[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]4[1f]0[1f]20240509120744[1f][1f][1f][1f]4[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091207443633[1f]TC=9A96823506E70119[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00F6[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]A
{}[2024-05-09 12:07:53.194]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_RETURN Success!
{}[2024-05-09 12:07:53.226]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:09:10.759]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:09:10.791]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:09:10.822]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:09:10.838]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:09:13.864]ERROR:[POSLink.cpp-3016 ] POSLink::PosLink::doHttp [ 1 ] {**************} : ProcessTransResult Msg: POSLINK COMMUNICATION ERROR: CONNECT ERROR
{}[2024-05-09 12:09:13.895]ERROR:[POSLink.cpp-3017 ] POSLink::PosLink::doHttp [ 1 ] {**************} : Error Message: No connection could be made because the target machine actively refused it **************:10009
{}[2024-05-09 12:09:13.911]ERROR:[POSLink.cpp-3018 ] POSLink::PosLink::doHttp [ 1 ] {**************} : NativeErrorCode: 10061
{}[2024-05-09 12:09:13.926]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:10:15.181]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:10:15.212]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:10:15.259]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:10:15.290]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:10:15.603]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]2.12[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]L
{}[2024-05-09 12:10:23.444]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000726[1f]000726[1f]03RLEB7EUT558T38QZU[1f]5[1f]050906[1f][1f][1f][1c]03[1c]212[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]5[1f]1[1f]20240509121015[1f][1f][1f][1f]5[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091210157071[1f]TC=FB68056E608619C7[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00F7[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]T
{}[2024-05-09 12:10:23.491]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:10:23.538]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:10:33.127]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:10:33.159]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:10:33.190]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:10:33.221]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:10:33.418]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]4.12[1c][1c]0[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]c
{}[2024-05-09 12:10:33.903]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:10:33.934]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:10:33.965]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:10:43.776]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:10:43.792]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:10:43.808]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:10:43.839]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:10:44.183]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]4.12[1c][1c]0[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]c
{}[2024-05-09 12:10:44.636]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:10:44.651]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:10:44.667]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:11:07.322]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:11:07.354]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:11:07.369]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:11:07.385]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:11:07.526]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]4.12[1c][1c]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]L
{}[2024-05-09 12:11:14.115]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100021[1c]ALREADY COMPL.[03]o
{}[2024-05-09 12:11:14.146]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =ALREADY COMPL.
{}[2024-05-09 12:11:14.177]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:11:18.490]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:11:18.505]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:11:18.521]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:11:18.552]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:11:18.896]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 12:11:20.631]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB7GYT04J1P9QT0[1f]0[1f]050906[1f][1f][1f][1c]2=0=0=0=0=0[1c]-112=0=0=0=0=0[1c]20240509121119[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]B
{}[2024-05-09 12:11:20.662]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 12:11:20.693]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:11:39.693]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:11:39.724]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:11:39.756]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:11:39.787]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:11:40.084]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 12:11:40.662]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]100023[1c]NOT FOUND[03]M
{}[2024-05-09 12:11:40.693]ERROR:[POSLink.cpp-2066 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Batch_BATCHCLOSE_EDCType(NULL) Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:11:40.724]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:11:49.240]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:11:49.256]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:11:49.287]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:11:49.318]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:11:49.615]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]0.00[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]M
{}[2024-05-09 12:11:55.691]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100002[1c]ABORTED[03]0
{}[2024-05-09 12:11:55.722]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_AUTH Fail!,ResultTxt =ABORTED
{}[2024-05-09 12:11:55.753]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:12:00.232]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:12:00.263]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:12:00.294]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:12:00.325]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:12:00.372]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]1.06[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]J
{}[2024-05-09 12:12:07.274]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000728[1f]000728[1f]03RLEB7X4WKDRH8VQT2[1f]1[1f]050907[1f][1f][1f][1c]03[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509121159[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091211596887[1f]TC=FF085FC06A232D65[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00F8[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]Q
{}[2024-05-09 12:12:07.305]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:12:07.336]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:12:18.200]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:12:18.231]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:12:18.262]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:12:18.278]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:12:18.481]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]3.06[1c][1c]0[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]a
{}[2024-05-09 12:12:18.903]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:12:18.934]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:12:18.966]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:12:23.750]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:12:23.766]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:12:23.781]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:12:23.797]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:12:24.010]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]3.06[1c][1c]0[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]a
{}[2024-05-09 12:12:24.463]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:12:24.495]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:12:24.526]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:12:38.563]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:12:38.594]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:12:38.626]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:12:38.657]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:12:38.766]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]3.18[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]G
{}[2024-05-09 12:12:46.546]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000729[1f]000729[1f]03RLEB7JAY9JLQWLQT3[1f]2[1f]050907[1f][1f][1f][1c]03[1c]318[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240509121238[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091212385966[1f]TC=19FFD9E01888757E[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00F9[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]G
{}[2024-05-09 12:12:46.577]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:12:46.609]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:13:47.680]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:13:47.711]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:13:47.742]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:13:47.774]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:13:47.883]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 12:13:49.680]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB7LAFPEP2D0QT6[1f]0[1f]050907[1f][1f][1f][1c]0=0=0=0=0=0[1c]0=0=0=0=0=0[1c]20240509121348[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]z
{}[2024-05-09 12:13:49.711]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 12:13:49.742]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:15:01.187]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:15:01.218]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:15:01.265]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:15:01.296]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:15:01.609]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]1.06[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]J
{}[2024-05-09 12:15:08.948]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000736[1f]000736[1f]03RLEB7NLQ3Z3PFKQT9[1f]1[1f]050908[1f][1f][1f][1c]03[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509121501[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091215017528[1f]TC=A8072AF99B02AAEA[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00FA[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]5
{}[2024-05-09 12:15:08.995]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:15:09.026]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:15:16.514]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:15:16.545]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:15:16.576]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:15:16.608]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:15:16.670]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]2.06[1c][1c]0[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]`
{}[2024-05-09 12:15:17.092]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:15:17.123]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:15:17.155]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:15:21.692]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:15:21.723]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:15:21.754]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:15:21.785]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:15:21.887]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]2.06[1c][1c]0[1f][1f][1f]0[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]`
{}[2024-05-09 12:15:22.309]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:15:22.340]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:15:22.371]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:15:26.572]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:15:26.587]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:15:26.618]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:15:26.650]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:15:26.806]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 12:15:28.540]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB7YAVHA4LVVQTA[1f]0[1f]050908[1f][1f][1f][1c]0=0=0=0=0=0[1c]0=0=0=0=0=0[1c]20240509121527[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]z
{}[2024-05-09 12:15:28.572]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 12:15:28.587]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:16:59.584]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:16:59.615]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:16:59.662]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:16:59.694]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:16:59.986]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]1.06[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]J
{}[2024-05-09 12:17:06.699]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000741[1f]000741[1f]03RLEB7R8YD7AR5KQTD[1f]1[1f]050909[1f][1f][1f][1c]03[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509121659[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091216596363[1f]TC=230ADBE468E86791[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00FB[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]I
{}[2024-05-09 12:17:06.730]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:17:06.761]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:18:59.530]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:18:59.562]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:18:59.593]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:18:59.608]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:18:59.702]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 12:19:01.624]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB7UR2HY9F0EQTF[1f]0[1f]050909[1f][1f][1f][1c]0=0=0=0=0=0[1c]0=0=0=0=0=0[1c]20240509121900[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]d
{}[2024-05-09 12:19:01.655]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 12:19:01.687]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:19:10.785]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:19:10.816]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:19:10.863]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:19:10.894]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:19:11.066]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]2.12[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]L
{}[2024-05-09 12:19:18.581]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000750[1f]000750[1f]03RLEB7V9GFK2W16QTH[1f]1[1f]050910[1f][1f][1f][1c]03[1c]212[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509121910[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091219100910[1f]TC=0E1D7108B4B17AEA[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00FC[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03],
{}[2024-05-09 12:19:18.613]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:19:18.644]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:19:23.554]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:19:23.585]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:19:23.617]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:19:23.632]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:19:23.976]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]4.12[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]c
{}[2024-05-09 12:19:27.955]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB7VXPY2G8A5QTX[1f]2[1f]050910[1f][1f][1f][1c]04[1c]412[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240509121923[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091219238527[1f]TC=0E1D7108B4B17AEA[1f]TVR=0000000000[1f]ATC=00FC[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]m
{}[2024-05-09 12:19:27.987]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 12:19:28.018]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:19:37.633]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:19:37.665]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:19:37.696]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:19:37.727]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:19:37.790]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]0.00[1c][1c]2[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]N
{}[2024-05-09 12:19:42.741]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100002[1c]ABORTED[03]0
{}[2024-05-09 12:19:42.772]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_AUTH Fail!,ResultTxt =ABORTED
{}[2024-05-09 12:19:42.803]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:19:45.975]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:19:46.007]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:19:46.038]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:19:46.062]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:19:46.406]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]2.12[1c][1c]2[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]O
{}[2024-05-09 12:19:53.222]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000755[1f]000755[1f]03RLEB80BB6FGB9KQTJ[1f]3[1f]050910[1f][1f][1f][1c]03[1c]212[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]3[1f]2[1f]20240509121945[1f][1f][1f][1f]3[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091219454626[1f]TC=3E9428608EE8A130[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00FD[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]$
{}[2024-05-09 12:19:53.253]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:19:53.284]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:19:56.050]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:19:56.081]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:19:56.097]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:19:56.128]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:19:56.231]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]02[1c]2.12[1c][1c]2[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]N
{}[2024-05-09 12:20:04.992]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000756[1f]000756[1f]03RLEB80MPW4X14XQTK[1f]4[1f]050910[1f][1f][1f][1c]02[1c]212[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]4[1f]2[1f]20240509121955[1f][1f][1f][1f]4[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091219553527[1f]TC=D9529B41F4000629[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00FE[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]$
{}[2024-05-09 12:20:05.023]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_RETURN Success!
{}[2024-05-09 12:20:05.055]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:20:20.343]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:20:20.359]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:20:20.390]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:20:20.421]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:20:20.499]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]6.36[1c][1c]4[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]K
{}[2024-05-09 12:20:27.547]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000757[1f]000757[1f]03RLEB81WT4G2D6JQTM[1f]5[1f]050910[1f][1f][1f][1c]03[1c]636[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]5[1f]4[1f]20240509122020[1f][1f][1f][1f]5[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091220207774[1f]TC=7DAD32221797F7E7[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00FF[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]"
{}[2024-05-09 12:20:27.578]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:20:27.610]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:20:27.656]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:20:27.672]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:20:27.703]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:20:27.719]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:20:27.750]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]6.36[1c][1c]4[1f][1f][1f]4[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]g
{}[2024-05-09 12:20:28.266]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:20:28.297]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:20:28.328]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:21:40.864]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:21:40.895]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:21:40.926]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:21:40.957]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:21:41.598]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]6.36[1c][1c]4[1f][1f][1f]4[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]g
{}[2024-05-09 12:21:42.217]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:21:42.248]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:21:42.290]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:21:48.028]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:21:48.060]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:21:48.091]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:21:48.122]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:21:48.367]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 12:21:50.257]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB83VJG9PGGUQTY[1f]0[1f]050910[1f][1f][1f][1c]2=0=0=0=0=0[1c]200=0=0=0=0=0[1c]20240509122149[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03][00]
{}[2024-05-09 12:21:50.288]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 12:21:50.320]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:22:09.881]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:22:09.912]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:22:09.959]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:22:09.990]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:22:10.178]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]1.06[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]J
{}[2024-05-09 12:22:22.829]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000760[1f]000760[1f]03RLEB84TG0Y9H7FQTR[1f]1[1f]050911[1f][1f][1f][1c]03[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509122209[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091222097979[1f]TC=6052959CB7AC866E[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0100[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]?
{}[2024-05-09 12:22:22.876]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:22:22.907]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:22:22.954]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:22:22.986]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:22:23.017]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:22:23.032]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:22:23.064]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]1.06[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]c
{}[2024-05-09 12:22:26.980]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB851N8YMTWTQTZ[1f]2[1f]050911[1f][1f][1f][1c]04[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240509122222[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091222227559[1f]TC=6052959CB7AC866E[1f]TVR=0000000000[1f]ATC=0100[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]h
{}[2024-05-09 12:22:27.012]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 12:22:27.043]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:22:39.173]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:22:39.204]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:22:39.235]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:22:39.251]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:22:39.345]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]02[1c]1.06[1c][1c]2[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]H
{}[2024-05-09 12:22:47.078]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000761[1f]000761[1f]03RLEB85L15D4XUHQTT[1f]3[1f]050911[1f][1f][1f][1c]02[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]3[1f]2[1f]20240509122238[1f][1f][1f][1f]3[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091222389095[1f]TC=2BCF09A1C505F3A9[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0101[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]4
{}[2024-05-09 12:22:47.109]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_RETURN Success!
{}[2024-05-09 12:22:47.141]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:22:57.383]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:22:57.414]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:22:57.445]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:22:57.476]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:22:58.101]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]10.60[1c][1c]4[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]
{}[2024-05-09 12:23:00.648]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100003[1c]DECLINE[1c][1f]2-Chip Read Error[1f][1f][1f][1f]50911[1f][1f][1f][1c]03[1c]0[1f][1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f][1f][1f][1c][1f]2[1f][1f][1f][1f][1f][1f][1f][1f][1f]0[1f][1f][1f][1f]null[1f][1f][1c][1f]4[1f]20240509122257[1f][1f][1f][1f][1f]null[1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091222571818[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]*
{}[2024-05-09 12:23:00.679]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_AUTH Fail!,ResultTxt =DECLINE
{}[2024-05-09 12:23:00.711]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:23:06.931]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:23:06.962]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:23:06.993]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:23:07.009]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:23:07.306]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]10.60[1c][1c]4[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]
{}[2024-05-09 12:23:14.284]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000762[1f]000762[1f]03RLEB86FMX5F0M2QTV[1f]4[1f]050911[1f][1f][1f][1c]03[1c]1060[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]4[1f]4[1f]20240509122306[1f][1f][1f][1f]4[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091223063081[1f]TC=757A14DF6FC3FE64[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0102[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][1b]
{}[2024-05-09 12:23:14.315]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:23:14.347]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:23:14.394]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:23:14.425]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:23:14.456]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:23:14.472]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:23:14.503]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]10.60[1c][1c]4[1f][1f][1f]4[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]S
{}[2024-05-09 12:23:18.294]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB86JZ4NQX6LQU0[1f]5[1f]050911[1f][1f][1f][1c]04[1c]1060[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]5[1f]4[1f]20240509122314[1f][1f][1f][1f]5[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091223144992[1f]TC=757A14DF6FC3FE64[1f]TVR=0000000000[1f]ATC=0102[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]*
{}[2024-05-09 12:23:18.326]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 12:23:18.357]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:23:26.110]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:23:26.141]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:23:26.172]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:23:26.204]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:23:26.360]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]2.12[1c][1c]6[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]K
{}[2024-05-09 12:23:33.865]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 000763[1f]000763[1f]03RLEB872XNF3GLDQU1[1f]6[1f]050911[1f][1f][1f][1c]03[1c]212[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]6[1f]6[1f]20240509122325[1f][1f][1f][1f]6[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091223250957[1f]TC=4D1C7F8431366083[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0103[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]<
{}[2024-05-09 12:23:33.896]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:23:33.928]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:23:41.593]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:23:41.624]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:23:41.656]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:23:41.687]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:23:42.015]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]6.12[1c][1c]6[1f][1f][1f]6[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]a
{}[2024-05-09 12:23:45.889]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB87ENT88D1RQU2[1f]7[1f]050911[1f][1f][1f][1c]04[1c]612[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]7[1f]6[1f]20240509122341[1f][1f][1f][1f]7[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091223414423[1f]TC=4D1C7F8431366083[1f]TVR=0000000000[1f]ATC=0103[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]{
{}[2024-05-09 12:23:45.920]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 12:23:45.952]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:23:53.830]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:23:53.861]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:23:53.892]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:23:53.923]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:23:54.002]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 12:23:55.955]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB87Q94UBKVQQU3[1f]0[1f]050911[1f][1f][1f][1c]4=0=0=0=0=0[1c]1672=0=0=0=0=0[1c]20240509122355[1c]46[1c]901028[1c]FAILEDCOUNT=0[1f]SAFFAILEDCOUNT=0[1f]SAFFAILEDTOTAL=0[1f][03]7
{}[2024-05-09 12:23:55.986]DEBUG:[POSLink.cpp-2064 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Batch_BATCHCLOSE_EDCType(NULL) Success!
{}[2024-05-09 12:23:56.017]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:38:15.927]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:38:15.958]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:38:15.989]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:38:16.020]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:38:16.630]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]B00[1c]1.63[1c][1c][03]G
{}[2024-05-09 12:38:19.204]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]B01[1c]1.54[1c]100023[1c]NOT FOUND[03]M
{}[2024-05-09 12:38:19.251]ERROR:[POSLink.cpp-2066 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Batch_BATCHCLOSE_EDCType(NULL) Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:38:19.282]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:42:29.722]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:42:29.754]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:42:29.801]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:42:29.832]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:42:30.082]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]6.80[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]C
{}[2024-05-09 12:42:37.524]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001348[1f]001348[1f]03RLEB99VK56NAB5QUN[1f]1[1f]050912[1f][1f][1f][1c]03[1c]680[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]1[1f]1[1f]20240509124230[1f][1f][1f][1f]1[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091242308354[1f]TC=6F3D3EDD9A07F2CA[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0104[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]3
{}[2024-05-09 12:42:37.571]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:42:37.602]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:43:32.147]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:43:32.169]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:43:32.186]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:43:32.218]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:43:32.452]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]4.18[1c][1c]2[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]C
{}[2024-05-09 12:43:39.446]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001387[1f]001387[1f]03RLEB9BZ471X0GZQUP[1f]2[1f]050912[1f][1f][1f][1c]03[1c]418[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]2[1f]20240509124332[1f][1f][1f][1f]2[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091243328297[1f]TC=D48CC8DC00CD784C[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0105[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]Q
{}[2024-05-09 12:43:39.478]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:43:39.509]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:43:39.556]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:43:39.587]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:43:39.618]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:43:39.650]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:43:39.681]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]4.18[1c][1c]2[1f][1f][1f]2[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]i
{}[2024-05-09 12:43:43.483]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB9W0WH827RRQUQ[1f]3[1f]050912[1f][1f][1f][1c]04[1c]418[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]3[1f]2[1f]20240509124339[1f][1f][1f][1f]3[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091243397627[1f]TC=D48CC8DC00CD784C[1f]TVR=0000000000[1f]ATC=0105[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]f
{}[2024-05-09 12:43:43.514]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 12:43:43.545]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:44:23.489]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:44:23.520]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:44:23.551]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:44:23.583]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:44:23.755]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]8.36[1c][1c]4[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]E
{}[2024-05-09 12:44:30.896]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001405[1f]001405[1f]03RLEB9DEFNQDQ0LQUZ[1f]4[1f]050912[1f][1f][1f][1c]03[1c]836[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]4[1f]4[1f]20240509124424[1f][1f][1f][1f]4[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091244240309[1f]TC=6844F7176D7F3848[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0106[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]M
{}[2024-05-09 12:44:30.927]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:44:30.958]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:44:31.005]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:44:31.036]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:44:31.067]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:44:31.099]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:44:31.130]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]8.36[1c][1c]4[1f][1f][1f]4[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]i
{}[2024-05-09 12:44:34.984]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB9DXJL6JLMKQUT[1f]5[1f]050912[1f][1f][1f][1c]04[1c]836[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]5[1f]4[1f]20240509124431[1f][1f][1f][1f]5[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091244315230[1f]TC=6844F7176D7F3848[1f]TVR=0000000000[1f]ATC=0106[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]|
{}[2024-05-09 12:44:35.016]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 12:44:35.047]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:45:09.705]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:45:09.736]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:45:09.768]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:45:09.799]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:45:09.861]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]11.54[1c][1c]6[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]{
{}[2024-05-09 12:45:16.649]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001448[1f]001448[1f]03RLEB9ER1UW38YNQUU[1f]6[1f]050912[1f][1f][1f][1c]03[1c]1154[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]6[1f]6[1f]20240509124510[1f][1f][1f][1f]6[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091245107755[1f]TC=2F6390289A12DACD[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0107[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][0a]
{}[2024-05-09 12:45:16.680]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:45:16.711]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:45:36.701]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:45:36.732]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:45:36.764]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:45:36.779]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:45:36.857]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]2.12[1c][1c]7[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]J
{}[2024-05-09 12:45:43.639]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001459[1f]001459[1f]03RLEB9FLF5ALEXDQV0[1f]7[1f]050912[1f][1f][1f][1c]03[1c]212[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]7[1f]7[1f]20240509124537[1f][1f][1f][1f]7[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091245375524[1f]TC=61A7FA564B8AEF52[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0108[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]5
{}[2024-05-09 12:45:43.670]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:45:43.701]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:45:51.549]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:45:51.580]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:45:51.611]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:45:51.627]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:45:51.908]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]16[1c][1c][1c]7[1f][1f][1f]7[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]y
{}[2024-05-09 12:45:55.690]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001473[1f]001473[1f]03RLEB9G175X75D7QV1[1f][1f]050912[1f][1f][1f][1c]19[1c]212[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f]43415244484F4C4445522F56495341[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]8[1f]7[1f]20240509124552[1f][1f][1f][1f][1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091245522707[1f]TC=61A7FA564B8AEF52[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]2
{}[2024-05-09 12:45:55.721]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_VOID Success!
{}[2024-05-09 12:45:55.752]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:46:28.449]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:46:28.480]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:46:28.512]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:46:28.527]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:46:28.777]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]10.36[1c][1c]9[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]q
{}[2024-05-09 12:46:36.140]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001529[1f]001529[1f]03RLEB9H8LKUMA0JQV3[1f]9[1f]050912[1f][1f][1f][1c]03[1c]1036[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]9[1f]9[1f]20240509124629[1f][1f][1f][1f]9[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091246293337[1f]TC=31FFFE18D2B87FAC[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0109[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]p
{}[2024-05-09 12:46:36.172]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:46:36.187]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:46:36.234]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:46:36.250]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:46:36.281]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:46:36.297]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:46:36.328]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]10.36[1c][1c]9[1f][1f][1f]9[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]P
{}[2024-05-09 12:46:40.156]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB9HWZ4KPJF7QV4[1f]10[1f]050912[1f][1f][1f][1c]04[1c]1036[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]10[1f]9[1f]20240509124636[1f][1f][1f][1f]10[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091246363637[1f]TC=31FFFE18D2B87FAC[1f]TVR=0000000000[1f]ATC=0109[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]b
{}[2024-05-09 12:46:40.187]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 12:46:40.218]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:46:48.361]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:46:48.393]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:46:48.424]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:46:48.455]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:46:48.752]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]02[1c]10.36[1c][1c]10[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]H
{}[2024-05-09 12:46:55.835]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001538[1f]001538[1f]03RLEB9HRYVXVUADQV5[1f]11[1f]050912[1f][1f][1f][1c]02[1c]1036[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]11[1f]10[1f]20240509124649[1f][1f][1f][1f]11[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091246490361[1f]TC=3D45CA1759AAFD08[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=010A[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][12]
{}[2024-05-09 12:46:55.866]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_RETURN Success!
{}[2024-05-09 12:46:55.898]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:47:27.658]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:47:27.690]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:47:27.705]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:47:27.736]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:47:28.065]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]5.30[1c][1c]12[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]y
{}[2024-05-09 12:47:34.900]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001556[1f]001556[1f]03RLEB9J22PRJWGDQV7[1f]12[1f]050912[1f][1f][1f][1c]03[1c]530[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]12[1f]12[1f]20240509124728[1f][1f][1f][1f]12[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091247287301[1f]TC=A780FBAA50998137[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=010B[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03];
{}[2024-05-09 12:47:34.931]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:47:34.963]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:47:55.450]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:47:55.482]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:47:55.513]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:47:55.544]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:47:55.716]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]11.66[1c][1c]13[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]N
{}[2024-05-09 12:48:02.540]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001557[1f]001557[1f]03RLEB9JT36X0VLKQV8[1f]13[1f]050912[1f][1f][1f][1c]03[1c]1166[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]13[1f]13[1f]20240509124756[1f][1f][1f][1f]13[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091247563547[1f]TC=A3629D23C6A2EDD7[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=010C[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]{
{}[2024-05-09 12:48:02.571]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:48:02.603]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:48:54.602]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:48:54.633]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:48:54.664]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:48:54.680]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:48:55.323]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]5.30[1c][1c]14[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]
{}[2024-05-09 12:49:02.084]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001558[1f]001558[1f]03RLEB9LN8DP3PRFQVA[1f]14[1f]050912[1f][1f][1f][1c]03[1c]530[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]14[1f]14[1f]20240509124855[1f][1f][1f][1f]14[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091248556535[1f]TC=6E16055B77AAD9BD[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=010D[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]K
{}[2024-05-09 12:49:02.115]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:49:02.130]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:49:30.382]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:49:30.413]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:49:30.444]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:49:30.475]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:49:30.647]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]12.72[1c][1c]15[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]N
{}[2024-05-09 12:49:37.251]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001563[1f]001563[1f]03RLEB9MPHVXHEYMQVW[1f]15[1f]050912[1f][1f][1f][1c]03[1c]1272[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]15[1f]15[1f]20240509124930[1f][1f][1f][1f]15[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091249308894[1f]TC=0A9D078C72C8850E[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=010E[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]t
{}[2024-05-09 12:49:37.282]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:49:37.314]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:50:51.076]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:50:51.107]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:50:51.138]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:50:51.170]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:50:51.435]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]16.96[1c][1c]16[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]C
{}[2024-05-09 12:50:58.181]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001586[1f]001586[1f]03RLEB9P8N3M7F6NQVE[1f]16[1f]050912[1f][1f][1f][1c]03[1c]1696[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]16[1f]16[1f]20240509125051[1f][1f][1f][1f]16[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091250513869[1f]TC=F3A06F56E97B3313[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=010F[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]{
{}[2024-05-09 12:50:58.213]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:50:58.244]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:51:10.045]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:51:10.077]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:51:10.108]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:51:10.139]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:51:10.186]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]17.96[1c][1c]16[1f][1f][1f]16[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]]
{}[2024-05-09 12:51:13.977]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB9PYA7RRVJYQVF[1f]17[1f]050912[1f][1f][1f][1c]04[1c]1796[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]17[1f]16[1f]20240509125110[1f][1f][1f][1f]17[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091251105740[1f]TC=F3A06F56E97B3313[1f]TVR=0000000000[1f]ATC=010F[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]<
{}[2024-05-09 12:51:14.008]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-05-09 12:51:14.040]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:51:47.277]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:51:47.309]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:51:47.340]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:51:47.356]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:51:47.559]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]6.36[1c][1c]17[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]y
{}[2024-05-09 12:51:54.334]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001587[1f]001587[1f]03RLEB9QVA9U4XBXQVH[1f]18[1f]050912[1f][1f][1f][1c]03[1c]636[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]18[1f]17[1f]20240509125147[1f][1f][1f][1f]18[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091251477178[1f]TC=6C95E78BA7E902EC[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0110[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]6
{}[2024-05-09 12:51:54.365]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:51:54.396]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:51:54.443]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:51:54.474]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:51:54.505]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:51:54.537]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:51:54.568]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]6.36[1c][1c]17[1f][1f][1f]17[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]g
{}[2024-05-09 12:51:55.052]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:51:55.084]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:51:55.115]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:52:07.311]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:52:07.343]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:52:07.374]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:52:07.405]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:52:07.436]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]6.36[1c][1c]17[1f][1f][1f]17[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]g
{}[2024-05-09 12:52:07.889]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:52:07.921]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:52:07.952]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:52:12.202]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:52:12.233]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:52:12.264]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:52:12.296]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:52:12.539]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]16[1c][1c][1c]17[1f][1f][1f]17[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]y
{}[2024-05-09 12:52:16.211]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLEB9RKV3LXXRZQVX[1f][1f]050912[1f][1f][1f][1c]20[1c]1796[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]19[1f]17[1f]20240509125212[1f][1f][1f][1f][1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091252122811[1f]TC=F3A06F56E97B3313[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]T
{}[2024-05-09 12:52:16.242]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_VOID Success!
{}[2024-05-09 12:52:16.273]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:53:00.170]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:53:00.201]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:53:00.233]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:53:00.264]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:53:00.461]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]18.96[1c][1c]19[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]B
{}[2024-05-09 12:53:07.361]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 001588[1f]001588[1f]03RLEB9T6P6A190AQVK[1f]20[1f]050912[1f][1f][1f][1c]03[1c]1896[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]20[1f]19[1f]20240509125300[1f][1f][1f][1f]20[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202405091253002243[1f]TC=7D0DF4F2798C1889[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=0111[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]i
{}[2024-05-09 12:53:07.393]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-05-09 12:53:07.424]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-05-09 12:53:07.471]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-05-09 12:53:07.502]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-05-09 12:53:07.533]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-05-09 12:53:07.549]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-05-09 12:53:07.580]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]18.96[1c][1c]19[1f][1f][1f]19[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]R
{}[2024-05-09 12:53:08.064]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-05-09 12:53:08.096]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_POSTAUTH Fail!,ResultTxt =NOT FOUND
{}[2024-05-09 12:53:08.127]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
