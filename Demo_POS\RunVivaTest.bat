@echo off
echo ========================================
echo    VIVA WALLET INTEGRATION TEST
echo ========================================
echo.
echo Testing your PAX A920 Pro Terminal (6628383)
echo with PRODUCTION Viva Wallet credentials
echo.

REM Build the project
echo Building project...
dotnet build French_Press_POS.csproj --configuration Debug --verbosity quiet

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Build failed! Check for compilation errors.
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.

REM Run the main application
echo Starting POS application...
echo.
echo 🚀 INSTRUCTIONS:
echo 1. The POS application will open
echo 2. Navigate to an order (Form2)
echo 3. Look for "Manual Entry (1 EUR/USD)" button
echo 4. Click it to test Viva Wallet payment
echo 5. Use test card: ****************
echo 6. Expiry: 12/25, CVV: 123
echo 7. Auth Code: 123789 (pre-filled)
echo 8. Verify receipt shows Terminal 6628383
echo 9. Confirm Function 101.3 is enabled
echo.

pause

REM Start the application
start "" "bin\Debug\French_Press_POS.exe"

echo.
echo ✅ Application started!
echo.
echo Your PAX A920 Pro terminal is now configured as the payment processor.
echo Check the application for Viva Wallet payment options.
echo.
pause
