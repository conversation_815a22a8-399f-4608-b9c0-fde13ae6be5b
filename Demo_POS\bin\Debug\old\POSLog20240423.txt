{}[2024-04-23 10:31:55.556]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-23 10:31:55.587]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-23 10:31:55.618]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-23 10:31:55.634]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-23 10:31:56.556]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]1.06[1c][1c]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]J
{}[2024-04-23 10:32:06.295]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 061912[1f]061912[1f]03RLD1RFFN8YAXR5193[1f]5[1f]042203[1f][1f][1f][1c]03[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]2[1f]1[1f]20240423103157[1f][1f][1f][1f]5[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404231031573265[1f]TC=5996E577CB88E689[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00E8[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]_
{}[2024-04-23 10:32:06.326]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-23 10:32:06.341]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-23 10:32:06.388]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-23 10:32:06.404]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-23 10:32:06.420]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-23 10:32:06.435]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-23 10:32:06.466]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]1.06[1c][1c]1[1f][1f][1f]1[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]c
{}[2024-04-23 10:32:10.821]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLD1RFKD7K225Z194[1f]6[1f]042203[1f][1f][1f][1c]04[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]3[1f]1[1f]20240423103206[1f][1f][1f][1f]6[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404231032068033[1f]TC=7EF180F88F27CF77[1f]TVR=0000000000[1f]ATC=00E7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]w
{}[2024-04-23 10:32:10.837]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-04-23 10:32:10.852]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-23 10:32:22.706]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-23 10:32:22.721]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-23 10:32:22.737]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-23 10:32:22.752]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-23 10:32:22.979]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]3.18[1c][1c]3[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]E
{}[2024-04-23 10:32:30.678]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 061914[1f]061914[1f]03RLD1RG7LXTU7NV195[1f]7[1f]042203[1f][1f][1f][1c]03[1c]318[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]4[1f]3[1f]20240423103223[1f][1f][1f][1f]7[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404231032235690[1f]TC=EF989D682CB3A2A9[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00E9[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]8
{}[2024-04-23 10:32:30.709]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-23 10:32:30.741]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-23 10:32:38.392]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-23 10:32:38.408]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-23 10:32:38.423]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-23 10:32:38.439]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-23 10:32:38.642]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]16[1c][1c][1c]3[1f][1f][1f]3[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]y
{}[2024-04-23 10:32:42.468]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLD1RGJEPQ0VBV196[1f][1f]042203[1f][1f][1f][1c]20[1c]106[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]5[1f]3[1f]20240423103238[1f][1f][1f][1f][1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404231032383519[1f]TC=7EF180F88F27CF77[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]d
{}[2024-04-23 10:32:42.500]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_VOID Success!
{}[2024-04-23 10:32:42.515]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-23 10:32:53.947]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-23 10:32:53.963]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-23 10:32:53.994]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-23 10:32:54.025]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-23 10:32:54.306]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]6.36[1c][1c]5[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]J
{}[2024-04-23 10:33:02.584]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 061915[1f]061915[1f]03RLD1RH6MGZ9JJN198[1f]9[1f]042203[1f][1f][1f][1c]03[1c]636[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]6[1f]5[1f]20240423103254[1f][1f][1f][1f]9[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404231032545486[1f]TC=B5BE6FF530A2C7DF[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00EA[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]Y
{}[2024-04-23 10:33:02.616]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-23 10:33:02.640]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-23 10:33:16.614]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-23 10:33:16.645]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-23 10:33:16.660]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-23 10:33:16.676]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-23 10:33:16.739]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]03[1c]11.66[1c][1c]6[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]z
{}[2024-04-23 10:33:24.171]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL 061916[1f]061916[1f]03RLD1RHRT7LHRJV199[1f]10[1f]042203[1f][1f][1f][1c]03[1c]1166[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]2[1f]1127[1f][1f][1f][1f]01[1f]CARDHOLDER/VISA[1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]7[1f]6[1f]20240423103317[1f][1f][1f][1f]10[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404231033174097[1f]TC=3474F06E6AAF0140[1f]TVR=0000000000[1f]AID=A0000000031010[1f]TSI=0000[1f]ATC=00EB[1f]APPLAB=VISA CREDIT[1f]APPPN=CHASE VISA[1f]IAD=06021203A00000[1f]CVM=7[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03][04]
{}[2024-04-23 10:33:24.202]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_AUTH Success!
{}[2024-04-23 10:33:24.234]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-23 10:33:24.280]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-23 10:33:24.296]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-23 10:33:24.312]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-23 10:33:24.327]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-23 10:33:24.359]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]04[1c]11.66[1c][1c]6[1f][1f][1f]6[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]T
{}[2024-04-23 10:33:28.275]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]000000[1c]OK[1c]0[1f]APPROVAL[1f][1f]03RLD1RX09NNT9BR19A[1f]11[1f]042203[1f][1f][1f][1c]04[1c]1166[1f]0[1f]0[1f]0[1f]0[1f]0[1f][1f][1f][1f][1f][1f]0[1f]0[1f]0[1c]4632[1f]0[1f]1127[1f][1f][1f][1f]01[1f][1f][1f][1f]0[1f][1f][1f][1f][1f][1f][1c]8[1f]6[1f]20240423103324[1f][1f][1f][1f]11[1f][1c][1f][1f][1f][1f][1c][1c][1c][1f]EDCTYPE=CREDIT[1f]CARDBIN=414720[1f]PROGRAMTYPE=0[1f]SN=1851078720[1f]GLOBALUID=1851078720202404231033242452[1f]TC=B5BE6FF530A2C7DF[1f]TVR=0000000000[1f]ATC=00EA[1f]userLanguageStatus=1[1f][1c][1c][1c][1c][03]H
{}[2024-04-23 10:33:28.307]DEBUG:[POSLink.cpp-1977 ] POSLink::PosLink::unpackTrans [ 1 ]  {**************} : Payment_CREDIT_POSTAUTH Success!
{}[2024-04-23 10:33:28.338]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
{}[2024-04-23 10:33:36.645]DEBUG:[POSLink.cpp-229 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : POSLink DLL Version is:V1.12.00
{}[2024-04-23 10:33:36.676]DEBUG:[POSLink.cpp-230 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans Start...
{}[2024-04-23 10:33:36.707]DEBUG:[POSLink.cpp-250 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : timeout =60000
{}[2024-04-23 10:33:36.738]DEBUG:[POSLink.cpp-465 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : CommType = HTTP,IP =**************,Port =10009
{}[2024-04-23 10:33:37.020]DEBUG:[POSLink.cpp-2848 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Send Message =[02]T00[1c]1.63[1c]16[1c][1c][1c]7[1f][1f][1f]5[1c][1c][1c][1c][1c][1c][1c]0[1c][1c][1c][03]{
{}[2024-04-23 10:33:37.473]DEBUG:[POSLink.cpp-2852 ] POSLink::PosLink::ConvertMessage [ 1 ]  {**************} : Receive Message =[02]0[1c]T01[1c]1.54[1c]100023[1c]NOT FOUND[03][
{}[2024-04-23 10:33:37.504]ERROR:[POSLink.cpp-1979 ] POSLink::PosLink::unpackTrans [ 1 ] {**************} : Payment_CREDIT_VOID Fail!,ResultTxt =NOT FOUND
{}[2024-04-23 10:33:37.535]DEBUG:[POSLink.cpp-537 ] POSLink::PosLink::ProcessTrans [ 1 ]  {**************} : ProcessTrans End...
