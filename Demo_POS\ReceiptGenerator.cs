using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Text;
using System.Windows.Forms;

namespace French_Press_POS
{
    /// <summary>
    /// Receipt data structure for printing
    /// </summary>
    public class ReceiptData
    {
        public string TransactionId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; }
        public string AuthorizationCode { get; set; }
        public string MaskedCardNumber { get; set; }
        public DateTime Timestamp { get; set; }
        public string MerchantId { get; set; }
        public string TerminalId { get; set; }
        public bool Function101_3Enabled { get; set; }
        public string PaymentMethod { get; set; } = "Card";
        public bool IsContactless { get; set; }
    }

    /// <summary>
    /// Handles receipt generation and printing for Viva Wallet transactions
    /// </summary>
    public class ReceiptGenerator
    {
        private PrintDocument _printDocument;
        private ReceiptData _receiptData;
        private Font _headerFont;
        private Font _normalFont;
        private Font _boldFont;
        private Font _smallFont;

        public ReceiptGenerator()
        {
            _printDocument = new PrintDocument();
            _printDocument.PrintPage += PrintDocument_PrintPage;
            
            // Initialize fonts
            _headerFont = new Font("Arial", 12, FontStyle.Bold);
            _normalFont = new Font("Arial", 10, FontStyle.Regular);
            _boldFont = new Font("Arial", 10, FontStyle.Bold);
            _smallFont = new Font("Arial", 8, FontStyle.Regular);
        }

        /// <summary>
        /// Print receipt with transaction details
        /// </summary>
        public void PrintReceipt(ReceiptData receiptData)
        {
            try
            {
                _receiptData = receiptData;
                
                // Show print preview dialog for testing
                PrintPreviewDialog previewDialog = new PrintPreviewDialog();
                previewDialog.Document = _printDocument;
                previewDialog.ShowDialog();
                
                // Uncomment the line below for actual printing
                // _printDocument.Print();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error printing receipt: {ex.Message}", "Print Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            Graphics g = e.Graphics;
            float yPos = 20;
            float leftMargin = 20;
            float lineHeight = 20;

            // Header
            g.DrawString("VIVA WALLET RECEIPT", _headerFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight * 2;

            // Merchant Information
            g.DrawString("MERCHANT RECEIPT", _boldFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight;
            
            g.DrawString($"Merchant ID: {_receiptData.MerchantId}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight;
            
            g.DrawString($"Terminal ID: {_receiptData.TerminalId}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight;

            // Separator line
            g.DrawLine(Pens.Black, leftMargin, yPos, leftMargin + 250, yPos);
            yPos += lineHeight;

            // Transaction Details
            g.DrawString("TRANSACTION DETAILS", _boldFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight;

            g.DrawString($"Date/Time: {_receiptData.Timestamp:dd/MM/yyyy HH:mm:ss}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight;

            g.DrawString($"Transaction ID: {_receiptData.TransactionId}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight;

            g.DrawString($"Payment Method: {_receiptData.PaymentMethod}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight;

            if (_receiptData.IsContactless)
            {
                g.DrawString("CONTACTLESS PAYMENT", _boldFont, Brushes.Black, leftMargin, yPos);
                yPos += lineHeight;
            }

            // Card Information (masked)
            if (!string.IsNullOrEmpty(_receiptData.MaskedCardNumber))
            {
                g.DrawString($"Card: {_receiptData.MaskedCardNumber}", _normalFont, Brushes.Black, leftMargin, yPos);
                yPos += lineHeight;
            }

            // Authorization Code
            g.DrawString($"Auth Code: {_receiptData.AuthorizationCode}", _boldFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight;

            // Amount
            g.DrawString($"Amount: {_receiptData.Amount:F2} {_receiptData.Currency}", _headerFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight * 2;

            // Function 101.3 Status
            if (_receiptData.Function101_3Enabled)
            {
                g.DrawString("✓ FUNCTION 101.3 ENABLED", _boldFont, Brushes.Green, leftMargin, yPos);
                yPos += lineHeight;
                g.DrawString("Terminal properly configured for authorization", _smallFont, Brushes.Black, leftMargin, yPos);
                yPos += lineHeight;
            }

            // Separator line
            g.DrawLine(Pens.Black, leftMargin, yPos, leftMargin + 250, yPos);
            yPos += lineHeight;

            // Status
            g.DrawString("TRANSACTION APPROVED", _boldFont, Brushes.Green, leftMargin, yPos);
            yPos += lineHeight;

            // Footer
            yPos += lineHeight;
            g.DrawString("Thank you for your business!", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += lineHeight;
            
            g.DrawString("Powered by Viva Wallet", _smallFont, Brushes.Gray, leftMargin, yPos);
            yPos += lineHeight;
            
            g.DrawString($"Printed: {DateTime.Now:dd/MM/yyyy HH:mm:ss}", _smallFont, Brushes.Gray, leftMargin, yPos);
        }

        /// <summary>
        /// Generate receipt text for display or logging
        /// </summary>
        public string GenerateReceiptText(ReceiptData receiptData)
        {
            var receipt = new StringBuilder();
            
            receipt.AppendLine("========================================");
            receipt.AppendLine("           VIVA WALLET RECEIPT          ");
            receipt.AppendLine("========================================");
            receipt.AppendLine();
            receipt.AppendLine("MERCHANT RECEIPT");
            receipt.AppendLine($"Merchant ID: {receiptData.MerchantId}");
            receipt.AppendLine($"Terminal ID: {receiptData.TerminalId}");
            receipt.AppendLine("----------------------------------------");
            receipt.AppendLine();
            receipt.AppendLine("TRANSACTION DETAILS");
            receipt.AppendLine($"Date/Time: {receiptData.Timestamp:dd/MM/yyyy HH:mm:ss}");
            receipt.AppendLine($"Transaction ID: {receiptData.TransactionId}");
            receipt.AppendLine($"Payment Method: {receiptData.PaymentMethod}");
            
            if (receiptData.IsContactless)
            {
                receipt.AppendLine("CONTACTLESS PAYMENT");
            }
            
            if (!string.IsNullOrEmpty(receiptData.MaskedCardNumber))
            {
                receipt.AppendLine($"Card: {receiptData.MaskedCardNumber}");
            }
            
            receipt.AppendLine($"Auth Code: {receiptData.AuthorizationCode}");
            receipt.AppendLine();
            receipt.AppendLine($"Amount: {receiptData.Amount:F2} {receiptData.Currency}");
            receipt.AppendLine();
            
            if (receiptData.Function101_3Enabled)
            {
                receipt.AppendLine("✓ FUNCTION 101.3 ENABLED");
                receipt.AppendLine("Terminal properly configured for authorization");
            }
            
            receipt.AppendLine("----------------------------------------");
            receipt.AppendLine("TRANSACTION APPROVED");
            receipt.AppendLine();
            receipt.AppendLine("Thank you for your business!");
            receipt.AppendLine("Powered by Viva Wallet");
            receipt.AppendLine($"Printed: {DateTime.Now:dd/MM/yyyy HH:mm:ss}");
            receipt.AppendLine("========================================");
            
            return receipt.ToString();
        }

        /// <summary>
        /// Save receipt to file for record keeping
        /// </summary>
        public void SaveReceiptToFile(ReceiptData receiptData, string filePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    filePath = $"Receipt_{receiptData.TransactionId}_{DateTime.Now:yyyyMMddHHmmss}.txt";
                }

                var receiptText = GenerateReceiptText(receiptData);
                System.IO.File.WriteAllText(filePath, receiptText);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving receipt to file: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _printDocument?.Dispose();
            _headerFont?.Dispose();
            _normalFont?.Dispose();
            _boldFont?.Dispose();
            _smallFont?.Dispose();
        }
    }
}
