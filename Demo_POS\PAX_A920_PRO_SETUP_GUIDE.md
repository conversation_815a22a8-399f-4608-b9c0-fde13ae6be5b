# PAX A920 Pro Terminal Setup Guide for Viva Wallet Integration

## 🎯 **Your Terminal Details**
- **Terminal ID**: `6628383`
- **Device ID**: `cHxHjFYXRXqHJKVvZnSe2W`
- **Serial Number**: `2816bdb6488e0436`
- **Model**: PAX A920 Pro (SoftPos)
- **Status**: Active Terminal
- **Registered**: 20.08.2025 11:25:43

## 🔧 **Step-by-Step Setup Process**

### **Step 1: Complete Viva Wallet Configuration**

You need to get your API credentials from your Viva Wallet account:

1. **Login to Viva Wallet Developer Portal**
   - Go to: https://developer.viva.com
   - Login with your merchant account

2. **Get Your API Credentials**
   - Navigate to: **Settings** → **API Access**
   - Copy the following values:
     - **Merchant ID** (your merchant identifier)
     - **API Key** (for authentication)
     - **Client ID** (for OAuth)
     - **Client Secret** (for OAuth)

3. **Update App.config with Your Credentials**
   ```xml
   <!-- Replace these with your actual credentials -->
   <add key="VivaWallet.MerchantId" value="YOUR_ACTUAL_MERCHANT_ID" />
   <add key="VivaWallet.TerminalId" value="6628383" /> <!-- Already set -->
   <add key="VivaWallet.DeviceId" value="cHxHjFYXRXqHJKVvZnSe2W" /> <!-- Already set -->
   <add key="VivaWallet.SerialNumber" value="2816bdb6488e0436" /> <!-- Already set -->
   <add key="VivaWallet.ApiKey" value="YOUR_ACTUAL_API_KEY" />
   <add key="VivaWallet.ClientId" value="YOUR_ACTUAL_CLIENT_ID" />
   <add key="VivaWallet.ClientSecret" value="YOUR_ACTUAL_CLIENT_SECRET" />
   ```

### **Step 2: Configure Terminal as Payment Processor**

1. **In Viva Wallet Dashboard**:
   - Go to **Devices** → **Terminals**
   - Find your terminal: `6628383 Pax A920 Pro (SoftPos)`
   - Click **Configure** or **Settings**

2. **Enable Required Features**:
   - ✅ **Contactless Payments**: Enable NFC/Tap-to-Pay
   - ✅ **Function 101.3**: Enable for authorization handling
   - ✅ **API Integration**: Allow API-based transactions
   - ✅ **Receipt Printing**: Enable receipt generation

3. **Set Payment Methods**:
   - ✅ Visa, Mastercard, American Express
   - ✅ Contactless (NFC)
   - ✅ Chip & PIN
   - ✅ Manual Entry (for testing)

### **Step 3: Test Terminal Connection**

1. **Build and Run the Application**
   ```bash
   # In Visual Studio or command line
   msbuild Demo_POS/French_Press_POS.csproj
   ```

2. **Test Terminal Initialization**
   - The app will automatically try to connect to your terminal
   - Check console output for connection status
   - Look for: "✅ Terminal is active and ready for payments"

3. **Test Payment Processing**
   - Use the **Manual Entry** button for initial testing
   - Try **Contactless Payment** if terminal supports it

### **Step 4: Payment Flow with Your Terminal**

#### **Manual Payment (1 EUR/USD)**
1. Click **"Manual Entry (1 EUR/USD)"** button
2. Enter test card details:
   - **Amount**: 1.00 (pre-filled)
   - **Card**: **************** (Visa test card)
   - **Expiry**: 12/25
   - **CVV**: 123
   - **Auth Code**: 123789 (pre-filled)
3. Click **"Process Payment"**
4. Receipt will show your terminal ID and Function 101.3 enabled

#### **Contactless Payment**
1. Click **"Contactless Payment"** button
2. Terminal will prompt for card/phone tap
3. Customer taps card on PAX A920 Pro
4. Payment processes through Viva Wallet
5. Receipt generated with authorization code

### **Step 5: Verify Function 101.3 is Working**

The receipt will show:
```
✓ FUNCTION 101.3 ENABLED
Terminal properly configured for authorization
Auth Code: 123789
Terminal ID: 6628383
```

This confirms your terminal is properly configured as requested.

### **Step 6: Production Setup**

When ready for live payments:

1. **Switch to Production Environment**
   ```xml
   <add key="VivaWallet.Environment" value="Production" />
   <add key="VivaWallet.BaseUrl" value="https://api.vivapayments.com" />
   <add key="VivaWallet.AuthUrl" value="https://accounts.vivapayments.com" />
   ```

2. **Use Production Credentials**
   - Get production API keys from Viva Wallet
   - Update all credential values in App.config

3. **Test with Small Amounts**
   - Start with €0.01 or $0.01 transactions
   - Verify receipts and authorization codes
   - Confirm Function 101.3 is working

## 🔍 **Troubleshooting Your Terminal**

### **Common Issues**

1. **"Terminal not found" Error**
   - Verify Terminal ID `6628383` is correct
   - Check if terminal is active in Viva Wallet dashboard
   - Ensure API credentials are valid

2. **"Authentication Failed"**
   - Double-check Client ID and Client Secret
   - Verify API Key permissions
   - Ensure merchant account is active

3. **"Function 101.3 Not Enabled"**
   - Contact Viva Wallet support
   - Request Function 101.3 activation for terminal `6628383`
   - Verify terminal configuration in dashboard

4. **"Payment Declined"**
   - Check if terminal is in test mode for demo transactions
   - Verify card details are correct
   - Ensure sufficient funds for live transactions

### **Terminal Status Check**

The application includes a terminal status checker:
```csharp
var terminalIntegration = new VivaPaxTerminalIntegration();
var isReady = await terminalIntegration.InitializeTerminalAsync();
var info = await terminalIntegration.GetTerminalInfoAsync();
Console.WriteLine(info);
```

This will show:
- Terminal connection status
- Available payment methods
- Function 101.3 status
- Contactless capability

## 📞 **Support Contacts**

1. **Viva Wallet Technical Support**
   - For API and terminal configuration issues
   - Function 101.3 activation requests
   - Terminal status problems

2. **PAX Support**
   - For hardware-specific issues with A920 Pro
   - Terminal connectivity problems
   - Device configuration

## ✅ **Success Checklist**

- [ ] Viva Wallet credentials configured in App.config
- [ ] Terminal ID `6628383` recognized by application
- [ ] Authentication successful with Viva Wallet API
- [ ] Manual payment processing works
- [ ] Contactless payment processing works
- [ ] Receipts show authorization code 123789
- [ ] Function 101.3 confirmed enabled in receipts
- [ ] Terminal status shows "Active"

Once all items are checked, your PAX A920 Pro terminal is fully integrated with Viva Wallet and ready for payment processing!

## 🚀 **Next Steps**

1. **Get your API credentials** from Viva Wallet dashboard
2. **Update App.config** with real credentials
3. **Test manual payments** with 1 EUR/USD
4. **Test contactless payments** on your PAX A920 Pro
5. **Verify receipts** show Function 101.3 enabled
6. **Go live** with real transactions

Your terminal `6628383` is now configured as the payment processor/acquirer for the North-.NET-PAX-SI-SDK!
