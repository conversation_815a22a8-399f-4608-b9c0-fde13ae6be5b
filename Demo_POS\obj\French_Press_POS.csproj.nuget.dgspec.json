{"format": 1, "restore": {"D:\\workspace\\.c#\\North-.NET-PAX-SI-SDK\\Demo_POS\\French_Press_POS.csproj": {}}, "projects": {"D:\\workspace\\.c#\\North-.NET-PAX-SI-SDK\\Demo_POS\\French_Press_POS.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\workspace\\.c#\\North-.NET-PAX-SI-SDK\\Demo_POS\\French_Press_POS.csproj", "projectName": "French_Press_POS", "projectPath": "D:\\workspace\\.c#\\North-.NET-PAX-SI-SDK\\Demo_POS\\French_Press_POS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\workspace\\.c#\\North-.NET-PAX-SI-SDK\\Demo_POS\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"FileHelpers": {"target": "Package", "version": "[3.5.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}